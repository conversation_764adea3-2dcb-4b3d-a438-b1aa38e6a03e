import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import re
import os
import copy
import winsound
import threading
from .core import TextSearchEngine
from common.ui_components import ActionButton

class TextSearchWindow:
    def __init__(self, parent):
        self.parent = parent
        self.search_engine = TextSearchEngine()
        
        # 设置颜色主题
        self.bg_color = "#f8f9fa"
        self.panel_color = "#ffffff"
        self.text_color = "#212529"
        self.accent_color = "#4361ee"
        self.highlight_color = "#f72585"
        self.button_text_color = "#ffffff"  # 按钮文字颜色
        
        # 创建主框架
        self.frame = tk.Frame(parent, bg=self.bg_color, padx=15, pady=10)
        self.frame.pack(fill=tk.BOTH, expand=True)
        
        # 当前文件路径
        self.current_file_path = None

        # 标记是否正在编程操作文本，避免触发历史记录
        self._program_change = False

        # 历史记录栈，用于撤销操作（保存文本内容和光标位置）
        self.history = []  # 每个元素是 {"content": text, "cursor": position, "view": line}
        self.history_position = -1
        self.max_history = 20  # 最大历史记录数量

        # 自动搜索相关
        self.search_timer = None  # 搜索延迟定时器
        self.search_delay = 500  # 搜索延迟时间（毫秒）
        self.last_search_text = ""  # 上次搜索的文本
        
        # 添加顶部区域
        self.create_top_section()
        
        # 添加工具栏
        self.create_toolbar()
        
        # 添加中部区域
        self.create_main_section()
        
        # 添加底部区域
        self.create_bottom_section()
    
    def create_top_section(self):
        """创建顶部区域，包含文件导入和基本操作"""
        top_frame = tk.Frame(self.frame, bg=self.bg_color)
        top_frame.pack(fill=tk.X, pady=10)
        
        # 添加标题标签
        title_label = tk.Label(
            top_frame,
            text="文本搜索",
            font=("微软雅黑", 16, "bold"),
            fg=self.text_color,
            bg=self.bg_color
        )
        title_label.pack(side=tk.LEFT, padx=10)
        
        # 导入文件按钮
        self.import_btn = tk.Button(
            top_frame,
            text="导入文件",
            font=("微软雅黑", 10),
            bg=self.accent_color,
            fg=self.button_text_color,
            padx=15,
            pady=5,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.import_file
        )
        self.import_btn.pack(side=tk.RIGHT, padx=10)
        
        # 当前文件标签
        self.file_label = tk.Label(
            top_frame,
            text="未导入文件",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.bg_color
        )
        self.file_label.pack(side=tk.RIGHT, fill=tk.X, expand=True)
    
    def create_toolbar(self):
        """创建工具栏，包含保存、另存为和撤销按钮"""
        toolbar_frame = tk.Frame(self.frame, bg=self.bg_color)
        toolbar_frame.pack(fill=tk.X, pady=5)
        
        # 保存按钮
        self.save_btn = tk.Button(
            toolbar_frame,
            text="保存",
            font=("微软雅黑", 9),
            bg=self.accent_color,
            fg=self.button_text_color,
            padx=15,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.save_file,
            state=tk.DISABLED  # 初始状态为禁用
        )
        self.save_btn.pack(side=tk.LEFT, padx=5)
        
        # 另存为按钮
        self.save_as_btn = tk.Button(
            toolbar_frame,
            text="另存为",
            font=("微软雅黑", 9),
            bg=self.accent_color,
            fg=self.button_text_color,
            padx=15,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.save_file_as,
            state=tk.DISABLED  # 初始状态为禁用
        )
        self.save_as_btn.pack(side=tk.LEFT, padx=5)
        
        # 撤销按钮
        self.undo_btn = tk.Button(
            toolbar_frame,
            text="撤销",
            font=("微软雅黑", 9),
            bg=self.accent_color,
            fg=self.button_text_color,
            padx=15,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.undo_action,
            state=tk.DISABLED  # 初始状态为禁用
        )
        self.undo_btn.pack(side=tk.LEFT, padx=5)
    
    def create_main_section(self):
        """创建主区域，包含搜索和显示区域"""
        main_frame = tk.Frame(self.frame, bg=self.bg_color)
        main_frame.pack(fill=tk.BOTH, expand=True, pady=15)

        # 创建左右分栏 - 调整比例，搜索结果框往右调
        main_frame.columnconfigure(0, weight=2)  # 增加左侧权重
        main_frame.columnconfigure(1, weight=3)
        main_frame.rowconfigure(0, weight=1)

        # 左侧区域 - 搜索和结果（卡片式设计）
        left_card = tk.Frame(main_frame, bg=self.panel_color, relief=tk.RAISED, bd=1)
        left_card.grid(row=0, column=0, sticky="nsew", padx=(0, 15), pady=0)  # 增加右边距

        # 左侧内容框架（调整内边距适应小窗口）
        left_frame = tk.Frame(left_card, bg=self.panel_color, padx=12, pady=12)
        left_frame.pack(fill=tk.BOTH, expand=True)
        
        # 搜索区域标题
        search_title = tk.Label(
            left_frame,
            text="🔍 搜索与替换",
            font=("微软雅黑", 12, "bold"),
            fg=self.text_color,
            bg=self.panel_color,
            anchor="w"
        )
        search_title.pack(fill=tk.X, pady=(0, 15))

        # 搜索框架（改进布局）
        search_section = tk.LabelFrame(
            left_frame,
            text="搜索",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg=self.panel_color,
            padx=10,
            pady=8
        )
        search_section.pack(fill=tk.X, pady=(0, 10))

        # 搜索输入框
        self.search_var = tk.StringVar()
        self.search_entry = tk.Entry(
            search_section,
            textvariable=self.search_var,
            font=("微软雅黑", 11),
            bg="white",
            fg=self.text_color,
            insertbackground=self.text_color,
            relief=tk.FLAT,
            bd=0,
            highlightthickness=2,
            highlightcolor=self.accent_color
        )
        self.search_entry.pack(fill=tk.X, pady=(0, 10))

        # 绑定自动搜索事件
        self.search_var.trace('w', self.on_search_text_change)
        self.search_entry.bind("<Return>", lambda e: self.perform_search_immediate())
        self.search_entry.bind("<KeyPress>", self.on_search_key_press)

        # 搜索状态和清空按钮框架
        search_btn_frame = tk.Frame(search_section, bg=self.panel_color)
        search_btn_frame.pack(fill=tk.X)

        # 搜索状态标签
        self.search_status_label = tk.Label(
            search_btn_frame,
            text="输入任意字符自动搜索",
            font=("微软雅黑", 9),
            fg="#6c757d",
            bg=self.panel_color
        )
        self.search_status_label.pack(side=tk.LEFT)

        # 清空按钮
        self.clear_btn = tk.Button(
            search_btn_frame,
            text="🗑️ 清空",
            font=("微软雅黑", 10),
            bg="#6c757d",
            fg=self.button_text_color,
            relief=tk.FLAT,
            padx=15,
            pady=8,
            cursor="hand2",
            command=self.clear_search,
            state=tk.DISABLED  # 初始禁用
        )
        self.clear_btn.pack(side=tk.RIGHT)
        
        # 替换框架（改进布局）- 初始时隐藏
        self.replace_section = tk.LabelFrame(
            left_frame,
            text="替换",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg=self.panel_color,
            padx=10,
            pady=8
        )
        # 初始时不显示替换区域

        # 替换输入框
        self.replace_var = tk.StringVar()
        self.replace_entry = tk.Entry(
            self.replace_section,
            textvariable=self.replace_var,
            font=("微软雅黑", 11),
            bg="white",
            fg=self.text_color,
            insertbackground=self.text_color,
            relief=tk.FLAT,
            bd=0,
            highlightthickness=2,
            highlightcolor=self.accent_color
        )
        self.replace_entry.pack(fill=tk.X, pady=(0, 10))

        # 替换按钮区域
        replace_btn_frame = tk.Frame(self.replace_section, bg=self.panel_color)
        replace_btn_frame.pack(fill=tk.X)

        # 替换按钮
        self.replace_btn = tk.Button(
            replace_btn_frame,
            text="替换选中",
            font=("微软雅黑", 9),
            bg="#28a745",
            fg=self.button_text_color,
            relief=tk.FLAT,
            padx=15,
            pady=6,
            cursor="hand2",
            command=self.replace_selected
        )
        self.replace_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 全部替换按钮
        self.replace_all_btn = tk.Button(
            replace_btn_frame,
            text="全部替换",
            font=("微软雅黑", 9),
            bg="#dc3545",
            fg=self.button_text_color,
            relief=tk.FLAT,
            padx=15,
            pady=6,
            cursor="hand2",
            command=self.replace_all
        )
        self.replace_all_btn.pack(side=tk.LEFT)
        
        # 搜索结果区域 - 初始时隐藏
        self.result_section = tk.LabelFrame(
            left_frame,
            text="搜索结果",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg=self.panel_color,
            padx=10,
            pady=8
        )
        # 初始时不显示搜索结果区域

        # 结果统计标签（紧凑显示）
        self.result_count_label = tk.Label(
            self.result_section,
            text="0 个结果",
            font=("微软雅黑", 8),
            fg="#6c757d",
            bg=self.panel_color,
            anchor="w"
        )
        self.result_count_label.pack(fill=tk.X, pady=(0, 5))

        # 创建结果列表框架
        result_frame = tk.Frame(self.result_section, bg=self.panel_color)
        result_frame.pack(fill=tk.BOTH, expand=True)

        # 创建搜索结果列表（改进样式）
        self.result_list = tk.Listbox(
            result_frame,
            bg="white",
            fg=self.text_color,
            selectbackground=self.accent_color,
            selectforeground="white",
            font=("微软雅黑", 10),
            activestyle="none",
            relief=tk.FLAT,
            bd=0,
            highlightthickness=1,
            highlightcolor="#dee2e6"
        )
        self.result_list.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.result_list.bind("<<ListboxSelect>>", self.on_result_select)

        # 添加滚动条
        result_scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_list.yview)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.result_list.config(yscrollcommand=result_scrollbar.set)
        
        # 右侧区域 - 原文文本（卡片式设计）
        right_card = tk.Frame(main_frame, bg=self.panel_color, relief=tk.RAISED, bd=1)
        right_card.grid(row=0, column=1, sticky="nsew", padx=(10, 0), pady=0)

        # 右侧内容框架（调整内边距适应小窗口）
        right_frame = tk.Frame(right_card, bg=self.panel_color, padx=12, pady=12)
        right_frame.pack(fill=tk.BOTH, expand=True)

        # 原文区域标题
        text_title = tk.Label(
            right_frame,
            text="📄 文本内容",
            font=("微软雅黑", 12, "bold"),
            fg=self.text_color,
            bg=self.panel_color,
            anchor="w"
        )
        text_title.pack(fill=tk.X, pady=(0, 15))

        # 原文显示区域
        text_frame = tk.Frame(right_frame, bg=self.panel_color)
        text_frame.pack(fill=tk.BOTH, expand=True)

        # 创建原文文本框（改进样式）
        self.text_display = tk.Text(
            text_frame,
            wrap=tk.WORD,
            bg="white",
            fg=self.text_color,
            insertbackground=self.text_color,
            font=("微软雅黑", 11),
            padx=15,
            pady=15,
            relief=tk.FLAT,
            bd=0,
            highlightthickness=1,
            highlightcolor="#dee2e6",
            selectbackground=self.accent_color,
            selectforeground="white"
        )
        self.text_display.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 绑定文本变化事件
        self.text_display.bind("<<Modified>>", self.on_text_modified)

        # 添加滚动条
        text_scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.text_display.yview)
        text_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.text_display.config(yscrollcommand=text_scrollbar.set)

        # 添加水平滚动条
        h_text_frame = tk.Frame(right_frame, bg=self.panel_color)
        h_text_frame.pack(fill=tk.X, pady=(10, 0))

        h_text_scrollbar = ttk.Scrollbar(h_text_frame, orient="horizontal", command=self.text_display.xview)
        h_text_scrollbar.pack(fill=tk.X)
        self.text_display.config(xscrollcommand=h_text_scrollbar.set)
    
    def create_bottom_section(self):
        """创建底部状态栏"""
        bottom_frame = tk.Frame(self.frame, bg="#e9ecef", height=30)
        bottom_frame.pack(fill=tk.X, pady=5)
        
        # 状态标签
        self.status_label = tk.Label(
            bottom_frame,
            text="就绪",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg="#e9ecef",
            anchor="w",
            padx=10,
            pady=5
        )
        self.status_label.pack(fill=tk.X)
    
    def import_file(self):
        """导入文件功能"""
        file_path = filedialog.askopenfilename(
            title="选择文本文件",
            filetypes=[
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            try:
                # 清除之前的内容
                self._program_change = True  # 标记为程序操作
                self.text_display.delete("1.0", tk.END)
                
                # 清除结果列表
                self.result_list.delete(0, tk.END)
                
                # 读取文件内容
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                
                # 显示文件内容
                self.text_display.insert(tk.END, content)
                self.search_engine.set_text(content)
                
                # 保存当前文件路径
                self.current_file_path = file_path
                
                # 更新文件标签
                file_name = os.path.basename(file_path)
                self.file_label.config(text=f"当前文件: {file_name}")
                
                # 更新状态
                self.status_label.config(text=f"已加载文件: {file_name}")
                
                # 启用保存和另存为按钮
                self.save_btn.config(state=tk.NORMAL)
                self.save_as_btn.config(state=tk.NORMAL)
                
                # 清空历史记录并添加当前内容
                self.history = []
                self.history_position = -1
                self.add_history(content, "1.0", "1.0")  # 文件导入时从开头开始
                
                # 初始状态下禁用撤销按钮
                self.undo_btn.config(state=tk.DISABLED)
                
                # 解除程序操作标记
                self._program_change = False
            except Exception as e:
                messagebox.showerror("错误", f"无法读取文件: {str(e)}")
    
    def play_success_sound(self):
        """播放成功提示音"""
        def play_sound():
            try:
                # 播放系统提示音
                winsound.MessageBeep(winsound.MB_OK)
            except:
                pass  # 如果播放失败，静默忽略

        # 在后台线程播放音效，避免阻塞UI
        threading.Thread(target=play_sound, daemon=True).start()

    def show_success_panel(self, message):
        """显示成功提示面板"""
        # 创建临时提示窗口
        success_window = tk.Toplevel(self.frame)
        success_window.title("保存成功")
        success_window.geometry("300x120")
        success_window.resizable(False, False)
        success_window.configure(bg="#d4edda")

        # 居中显示
        success_window.transient(self.frame.winfo_toplevel())
        success_window.grab_set()

        # 计算居中位置
        parent = self.frame.winfo_toplevel()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - 150
        y = parent.winfo_y() + (parent.winfo_height() // 2) - 60
        success_window.geometry(f"300x120+{x}+{y}")

        # 成功图标和消息
        icon_label = tk.Label(
            success_window,
            text="✅",
            font=("微软雅黑", 24),
            bg="#d4edda",
            fg="#155724"
        )
        icon_label.pack(pady=(15, 5))

        message_label = tk.Label(
            success_window,
            text=message,
            font=("微软雅黑", 11),
            bg="#d4edda",
            fg="#155724"
        )
        message_label.pack(pady=(0, 10))

        # 确定按钮
        ok_btn = tk.Button(
            success_window,
            text="确定",
            font=("微软雅黑", 10),
            bg="#28a745",
            fg="white",
            relief=tk.FLAT,
            padx=20,
            pady=5,
            cursor="hand2",
            command=success_window.destroy
        )
        ok_btn.pack(pady=(0, 15))

        # 2秒后自动关闭
        success_window.after(2000, success_window.destroy)

        # 播放提示音
        self.play_success_sound()

    def save_file(self):
        """保存到原文件功能"""
        if not self.current_file_path:
            self.save_file_as()
            return

        try:
            # 获取当前文本内容
            content = self.text_display.get("1.0", tk.END)

            # 保存到原文件
            with open(self.current_file_path, "w", encoding="utf-8") as f:
                f.write(content)

            # 更新状态
            file_name = os.path.basename(self.current_file_path)
            self.status_label.config(text=f"文件已保存: {file_name}")

            # 显示成功提示面板和播放提示音
            self.show_success_panel(f"文件已成功保存：{file_name}")

        except Exception as e:
            messagebox.showerror("保存错误", f"无法保存文件: {str(e)}")
    
    def save_file_as(self):
        """另存为功能"""
        file_path = filedialog.asksaveasfilename(
            title="另存为",
            filetypes=[
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ],
            defaultextension=".txt"
        )
        
        if file_path:
            try:
                # 获取当前文本内容
                content = self.text_display.get("1.0", tk.END)
                
                # 保存到选定的文件
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                
                # 更新当前文件路径 - 保留原始文件路径，只显示新文件名但不切换
                original_path = self.current_file_path
                
                # 更新文件标签 - 显示刚保存的文件名
                file_name = os.path.basename(file_path)
                self.file_label.config(text=f"另存为: {file_name}")

                # 更新状态
                self.status_label.config(text=f"文件已另存为: {file_name}")

                # 启用保存按钮
                self.save_btn.config(state=tk.NORMAL)

                # 显示成功提示面板和播放提示音
                self.show_success_panel(f"文件已成功另存为：{file_name}")
            except Exception as e:
                messagebox.showerror("保存错误", f"无法保存文件: {str(e)}")
    
    def add_history(self, content, cursor_pos=None, view_line=None):
        """添加内容到历史记录栈，包含光标位置信息"""
        # 获取当前光标位置和可视行（如果没有提供的话）
        if cursor_pos is None:
            try:
                cursor_pos = self.text_display.index(tk.INSERT)
            except:
                cursor_pos = "1.0"

        if view_line is None:
            try:
                view_line = self.text_display.index("@0,0")
            except:
                view_line = "1.0"

        # 创建历史记录项
        history_item = {
            "content": content,
            "cursor": cursor_pos,
            "view": view_line
        }

        # 如果当前不在栈顶，移除当前位置之后的所有记录
        if self.history_position < len(self.history) - 1:
            self.history = self.history[:self.history_position + 1]

        # 如果历史记录为空或者新内容与最后一条不同，才添加
        if not self.history or content != self.history[-1]["content"]:
            # 添加新内容到历史记录
            self.history.append(history_item)
            self.history_position = len(self.history) - 1

            # 如果历史记录超过最大限制，移除最早的记录
            if len(self.history) > self.max_history:
                self.history.pop(0)
                self.history_position -= 1

            # 只有当有可撤销的历史时，才启用撤销按钮
            if len(self.history) > 1:  # 至少需要两条历史才能撤销
                self.undo_btn.config(state=tk.NORMAL)

            # 调试信息
            print(f"添加历史记录 - 当前位置: {self.history_position}, 历史长度: {len(self.history)}, 光标: {cursor_pos}")
    
    def undo_action(self):
        """撤销上一步操作"""
        # 调试信息
        print(f"尝试撤销 - 当前位置: {self.history_position}, 历史长度: {len(self.history)}")
        
        # 只有当历史记录中有超过一条记录且不在最前面时，才能撤销
        if len(self.history) > 1 and self.history_position > 0:
            # 标记为程序自动修改，避免触发历史记录
            self._program_change = True
            
            # 后退一步
            self.history_position -= 1
            
            # 恢复历史记录项
            history_item = self.history[self.history_position]
            content = history_item["content"]
            saved_cursor = history_item["cursor"]
            saved_view = history_item["view"]

            # 更新文本显示（保持程序修改标记）
            self.text_display.replace("1.0", tk.END, content)

            # 恢复光标位置和滚动位置
            try:
                # 获取文本的实际结束位置（不包括最后的换行符）
                actual_end = self.text_display.index("end-1c")

                # 验证保存的光标位置是否有效
                try:
                    # 尝试将保存的位置标准化
                    normalized_cursor = self.text_display.index(saved_cursor)

                    # 检查位置是否在有效范围内
                    if self.text_display.compare(normalized_cursor, "<=", actual_end):
                        self.text_display.mark_set(tk.INSERT, normalized_cursor)
                        cursor_to_show = normalized_cursor
                    else:
                        # 位置超出范围，设置到文本末尾
                        self.text_display.mark_set(tk.INSERT, actual_end)
                        cursor_to_show = actual_end

                except tk.TclError:
                    # 保存的光标位置无效，设置到文本开头
                    self.text_display.mark_set(tk.INSERT, "1.0")
                    cursor_to_show = "1.0"

                # 恢复滚动位置
                try:
                    normalized_view = self.text_display.index(saved_view)
                    if self.text_display.compare(normalized_view, "<=", actual_end):
                        self.text_display.see(normalized_view)
                    else:
                        # 如果保存的视图位置无效，显示光标位置
                        self.text_display.see(cursor_to_show)
                except tk.TclError:
                    # 保存的视图位置无效，显示光标位置
                    self.text_display.see(cursor_to_show)

                # 最后确保光标可见
                self.text_display.see(cursor_to_show)

                # 调试信息
                print(f"位置恢复: 保存的光标={saved_cursor}, 实际设置={cursor_to_show}, 文本结束={actual_end}")

            except Exception as e:
                # 如果所有恢复都失败，设置到文本开头
                print(f"位置恢复失败: {e}")
                self.text_display.mark_set(tk.INSERT, "1.0")
                self.text_display.see("1.0")

            # 更新搜索引擎中的文本
            self.search_engine.set_text(content)

            # 更新状态
            self.status_label.config(text="已撤销上一步操作")

            # 如果已经到达最早的历史记录，禁用撤销按钮
            if self.history_position <= 0:
                self.undo_btn.config(state=tk.DISABLED)
            else:
                # 如果还有更多历史记录可以撤销，保持按钮启用
                self.undo_btn.config(state=tk.NORMAL)

            # 不要立即解除程序自动修改标记，让文本修改事件处理完成后再解除
            # 这样可以避免撤销操作触发新的历史记录
            pass
            
            # 调试信息
            print(f"撤销成功 - 当前位置: {self.history_position}")
        else:
            print("无法撤销 - 没有更早的历史记录")
    
    def on_text_modified(self, event=None):
        """文本内容修改事件处理"""
        # 防止重复触发
        self.text_display.edit_modified(False)

        # 如果是程序自动修改，不记录历史
        if self._program_change:
            # 在撤销操作后，延迟解除程序修改标记
            self.text_display.after(10, lambda: setattr(self, '_program_change', False))
            return

        # 获取当前文本
        current_text = self.text_display.get("1.0", tk.END)

        # 添加到历史记录
        self.add_history(current_text)

        # 更新搜索引擎中的文本
        self.search_engine.set_text(current_text)

        # 如果有搜索文本，触发自动搜索
        if self.search_var.get():
            self.on_search_text_change()

        # 调试信息
        print("文本已修改，已记录历史")

    def on_search_text_change(self, *args):
        """搜索文本变化时的处理"""
        # 取消之前的搜索定时器
        if self.search_timer:
            self.frame.after_cancel(self.search_timer)
            self.search_timer = None

        search_text = self.search_var.get()

        # 如果搜索框为空，立即清空搜索结果
        if not search_text:
            self.clear_search_results()
            self.search_status_label.config(text="输入任意字符自动搜索", fg="#6c757d")
            return

        # 如果文本为空，不进行搜索
        if len(search_text.strip()) < 1:
            self.search_status_label.config(text="请输入搜索内容", fg="#ffc107")
            return

        # 设置延迟搜索定时器
        self.search_status_label.config(text="正在输入...", fg="#17a2b8")
        self.search_timer = self.frame.after(self.search_delay, self.perform_auto_search)

    def on_search_key_press(self, event):
        """搜索框按键事件"""
        # 如果按下Escape键，清空搜索
        if event.keysym == "Escape":
            self.clear_search()

    def perform_auto_search(self):
        """执行自动搜索"""
        search_text = self.search_var.get()

        # 如果搜索文本没有变化，不重复搜索
        if search_text == self.last_search_text:
            return

        self.last_search_text = search_text

        # 如果文本为空，不搜索
        if not search_text or len(search_text.strip()) < 1:
            return

        # 执行搜索
        self.perform_search_internal(search_text)

    def perform_search_immediate(self):
        """立即执行搜索（回车键触发）"""
        search_text = self.search_var.get()
        if search_text:
            self.perform_search_internal(search_text)

    def clear_search_results(self):
        """清空搜索结果但不清空搜索框"""
        # 清空搜索结果
        self.result_list.delete(0, tk.END)

        # 清除文本高亮
        self.text_display.tag_remove("search", "1.0", tk.END)
        self.text_display.tag_remove("current_line", "1.0", tk.END)

        # 隐藏替换和结果区域
        self.replace_section.pack_forget()
        self.result_section.pack_forget()

        # 禁用清空按钮
        self.clear_btn.config(state=tk.DISABLED)

        # 更新状态
        self.result_count_label.config(text="0 个结果")

    def clear_search(self):
        """清空搜索功能"""
        # 取消搜索定时器
        if self.search_timer:
            self.frame.after_cancel(self.search_timer)
            self.search_timer = None

        # 清空搜索框
        self.search_var.set("")
        self.replace_var.set("")
        self.last_search_text = ""

        # 清空搜索结果
        self.clear_search_results()

        # 更新状态
        self.status_label.config(text="已清空搜索")
        self.search_status_label.config(text="输入任意字符自动搜索", fg="#6c757d")

    def show_search_results(self):
        """显示搜索结果和替换区域（渐进式设计）"""
        # 显示搜索结果区域
        self.result_section.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 显示替换区域
        self.replace_section.pack(fill=tk.X, pady=(0, 10), before=self.result_section)

        # 启用清空按钮
        self.clear_btn.config(state=tk.NORMAL)

    def perform_search_internal(self, search_text):
        """内部搜索方法，带防卡死保护"""
        if not search_text:
            return

        try:
            # 更新搜索状态
            self.search_status_label.config(text="搜索中...", fg="#17a2b8")

            # 使用after方法异步执行搜索，防止界面卡死
            self.frame.after(10, lambda: self._do_search(search_text))

        except Exception as e:
            self.search_status_label.config(text="搜索出错", fg="#dc3545")
            print(f"搜索错误: {e}")

    def _do_search(self, search_text):
        """实际执行搜索的方法"""
        try:
            # 清除之前的搜索结果
            self.result_list.delete(0, tk.END)

            # 执行搜索
            results = self.search_engine.search(search_text)

            if not results:
                self.status_label.config(text=f"未找到匹配项: {search_text}")
                self.result_count_label.config(text="0 个结果")
                self.search_status_label.config(text="未找到结果", fg="#ffc107")
                # 即使没有结果也显示结果区域
                self.show_search_results()
                return

            # 限制显示结果数量，防止界面卡死
            # 单字符搜索结果可能很多，限制更严格
            if len(search_text) == 1:
                max_results = 500
            else:
                max_results = 1000
            display_results = results[:max_results]

            # 显示结果
            for idx, (line_num, line_text, start_pos) in enumerate(display_results):
                display_text = f"行 {line_num}: {line_text[:50]}..." if len(line_text) > 50 else f"行 {line_num}: {line_text}"
                self.result_list.insert(tk.END, display_text)

            # 更新状态和结果计数
            result_text = f"找到 {len(results)} 个匹配项"
            if len(results) > max_results:
                result_text += f"（显示前 {max_results} 个）"

            self.status_label.config(text=result_text)
            self.result_count_label.config(text=f"{len(results)} 个结果")
            self.search_status_label.config(text="搜索完成", fg="#28a745")

            # 为文本添加高亮
            self.highlight_search_text(search_text)

            # 显示搜索结果和替换区域（渐进式）
            self.show_search_results()

        except Exception as e:
            self.search_status_label.config(text="搜索出错", fg="#dc3545")
            self.status_label.config(text=f"搜索错误: {str(e)}")
            print(f"搜索错误: {e}")

    def perform_search(self):
        """保持兼容性的搜索方法"""
        search_text = self.search_var.get()
        if search_text:
            self.perform_search_internal(search_text)
    
    def highlight_search_text(self, search_text):
        """在原文中高亮显示搜索文本（带防卡死保护）"""
        self.text_display.tag_remove("search", "1.0", tk.END)

        try:
            start_pos = "1.0"
            highlight_count = 0
            # 单字符搜索高亮数量限制更严格
            if len(search_text) == 1:
                max_highlights = 200  # 单字符限制200个高亮
            else:
                max_highlights = 500  # 多字符限制500个高亮

            while highlight_count < max_highlights:
                start_pos = self.text_display.search(search_text, start_pos, stopindex=tk.END, nocase=True)
                if not start_pos:
                    break

                end_pos = f"{start_pos}+{len(search_text)}c"
                self.text_display.tag_add("search", start_pos, end_pos)
                start_pos = end_pos
                highlight_count += 1

            # 设置高亮样式
            self.text_display.tag_config("search", background=self.highlight_color, foreground="white")

            # 如果达到最大高亮数量，显示提示
            if highlight_count >= max_highlights:
                self.search_status_label.config(text=f"已高亮前{max_highlights}个匹配项", fg="#ffc107")

        except Exception as e:
            self.search_status_label.config(text="高亮出错", fg="#dc3545")
            print(f"高亮错误: {e}")
    
    def on_result_select(self, event):
        """当选择搜索结果时跳转到对应位置"""
        if not self.result_list.curselection():
            return
        
        try:
            # 获取选中的结果索引
            index = self.result_list.curselection()[0]
            
            # 获取对应的行号
            result_item = self.result_list.get(index)
            line_num = int(result_item.split(":")[0].split(" ")[1])
            
            # 跳转到对应位置
            self.text_display.see(f"{line_num}.0")
            
            # 高亮显示整行
            self.text_display.tag_remove("current_line", "1.0", tk.END)
            self.text_display.tag_add("current_line", f"{line_num}.0", f"{line_num}.end")
            self.text_display.tag_config("current_line", background=self.accent_color, foreground="white")
            
            # 更新状态
            self.status_label.config(text=f"已跳转到第 {line_num} 行")
        except Exception as e:
            messagebox.showerror("跳转错误", str(e))
    
    def replace_selected(self):
        """替换选中的搜索结果"""
        if not self.result_list.curselection():
            messagebox.showinfo("提示", "请先选择一个搜索结果")
            return
        
        search_text = self.search_var.get()
        replace_text = self.replace_var.get()
        
        if not search_text:
            return
        
        try:
            # 标记为程序自动修改
            self._program_change = True
            
            # 保存当前光标位置
            current_cursor = self.text_display.index(tk.INSERT)
            current_view = self.text_display.index("@0,0")

            # 获取当前文本内容作为历史记录
            current_text = self.text_display.get("1.0", tk.END)
            self.add_history(current_text, current_cursor, current_view)
            
            # 获取选中的结果索引
            index = self.result_list.curselection()[0]
            
            # 获取对应的行号
            result_item = self.result_list.get(index)
            line_num = int(result_item.split(":")[0].split(" ")[1])
            
            # 获取该行的内容
            line_content = self.text_display.get(f"{line_num}.0", f"{line_num}.end")
            
            # 替换首个匹配项
            new_content = line_content.replace(search_text, replace_text, 1)
            
            # 更新文本
            self.text_display.delete(f"{line_num}.0", f"{line_num}.end")
            self.text_display.insert(f"{line_num}.0", new_content)
            
            # 更新搜索引擎中的文本
            full_text = self.text_display.get("1.0", tk.END)
            self.search_engine.set_text(full_text)
            
            # 解除程序自动修改标记
            self._program_change = False
            
            # 重新执行搜索以更新结果列表
            self.perform_search()
            
            # 更新状态
            self.status_label.config(text=f"已替换第 {line_num} 行的匹配项")
        except Exception as e:
            messagebox.showerror("替换错误", str(e))
            self._program_change = False
    
    def replace_all(self):
        """替换所有匹配的搜索结果"""
        search_text = self.search_var.get()
        replace_text = self.replace_var.get()
        
        if not search_text:
            return
        
        try:
            # 标记为程序自动修改
            self._program_change = True
            
            # 保存当前光标位置
            current_cursor = self.text_display.index(tk.INSERT)
            current_view = self.text_display.index("@0,0")

            # 获取当前文本内容作为历史记录
            current_text = self.text_display.get("1.0", tk.END)
            self.add_history(current_text, current_cursor, current_view)

            # 替换所有匹配项
            new_text = current_text.replace(search_text, replace_text)

            # 统计替换的次数
            count = current_text.count(search_text)

            # 更新文本（使用replace方法保持位置）
            self.text_display.replace("1.0", tk.END, new_text)

            # 恢复光标位置
            try:
                actual_end = self.text_display.index("end-1c")
                if self.text_display.compare(current_cursor, "<=", actual_end):
                    self.text_display.mark_set(tk.INSERT, current_cursor)
                    self.text_display.see(current_cursor)
                else:
                    self.text_display.mark_set(tk.INSERT, actual_end)
                    self.text_display.see(actual_end)
            except:
                pass
            
            # 更新搜索引擎中的文本
            self.search_engine.set_text(new_text)
            
            # 解除程序自动修改标记
            self._program_change = False
            
            # 清除结果列表
            self.result_list.delete(0, tk.END)
            
            # 更新状态
            self.status_label.config(text=f"已替换 {count} 处匹配项")
        except Exception as e:
            messagebox.showerror("全部替换错误", str(e))
            self._program_change = False 