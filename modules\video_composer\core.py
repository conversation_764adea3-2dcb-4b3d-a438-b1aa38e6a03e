"""
视频合成核心处理模块 - 基于FFmpeg
高性能视频处理解决方案
"""

import os
import tempfile
import shutil
from datetime import datetime
import json
import psutil
import platform
import subprocess
from concurrent.futures import ThreadPoolExecutor, as_completed
import concurrent.futures
import threading
import time

# 导入FFmpeg视频合成器
try:
    from .ffmpeg_composer import FFmpegVideoComposer
    FFMPEG_COMPOSER_AVAILABLE = True
except ImportError:
    FFMPEG_COMPOSER_AVAILABLE = False
    print("警告: FFmpeg合成器导入失败")

# 视频合成FFmpeg进程管理器
class VideoFFmpegProcessManager:
    """视频合成FFmpeg进程管理器"""

    def __init__(self):
        self.active_processes = set()
        self.process_lock = threading.Lock()

    def register_process(self, process):
        """注册FFmpeg进程"""
        with self.process_lock:
            if hasattr(process, 'pid') and process.pid:
                self.active_processes.add(process.pid)
                print(f"注册视频FFmpeg进程: PID {process.pid}")

    def unregister_process(self, process):
        """注销FFmpeg进程"""
        with self.process_lock:
            if hasattr(process, 'pid') and process.pid:
                self.active_processes.discard(process.pid)
                print(f"注销视频FFmpeg进程: PID {process.pid}")

    def kill_all_video_ffmpeg_processes(self):
        """终止所有视频相关的FFmpeg进程"""
        killed_count = 0

        try:
            # 1. 终止我们跟踪的进程
            with self.process_lock:
                for pid in list(self.active_processes):
                    try:
                        if psutil.pid_exists(pid):
                            process = psutil.Process(pid)
                            if process.is_running():
                                process.terminate()
                                killed_count += 1
                                print(f"终止跟踪的视频FFmpeg进程: PID {pid}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                self.active_processes.clear()

            # 2. 查找并终止所有视频相关的FFmpeg进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower() if proc_info['name'] else ''

                    if ('ffmpeg' in proc_name or 'ffprobe' in proc_name):
                        cmdline = proc_info.get('cmdline', [])
                        # 检查是否是视频处理相关的命令
                        if cmdline and any('mp4' in arg or 'avi' in arg or 'mkv' in arg or 'mov' in arg or 'video' in arg for arg in cmdline):
                            proc.terminate()
                            killed_count += 1
                            print(f"终止视频FFmpeg进程: {proc_name} PID {proc_info['pid']}")

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            # 3. 强制杀死残留进程
            time.sleep(1)
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower() if proc_info['name'] else ''

                    if ('ffmpeg' in proc_name or 'ffprobe' in proc_name):
                        proc.kill()
                        killed_count += 1
                        print(f"强制杀死视频FFmpeg进程: {proc_name} PID {proc_info['pid']}")

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            if killed_count > 0:
                print(f"视频合成：总共终止了 {killed_count} 个FFmpeg进程")
            else:
                print("视频合成：没有发现需要终止的FFmpeg进程")

        except Exception as e:
            print(f"终止视频FFmpeg进程时出错: {e}")

# 全局视频FFmpeg进程管理器
video_ffmpeg_manager = VideoFFmpegProcessManager()


class VideoComposerProcessor:
    """视频合成处理核心类 - 基于FFmpeg"""

    def __init__(self):
        # 停止标志
        self.should_stop = False

        # FFmpeg合成器实例
        self.ffmpeg_composer = None
        if FFMPEG_COMPOSER_AVAILABLE:
            self.ffmpeg_composer = FFmpegVideoComposer()
        else:
            raise Exception("FFmpeg合成器不可用，请检查FFmpeg安装和配置")

        # 默认设置
        self.default_settings = {
            'resolution': '1920x1080',  # 分辨率
            'fps': 30,                  # 帧率
            'video_bitrate': '2000k',   # 视频码率
            'audio_bitrate': '128k',    # 音频码率
            'format': 'mp4',            # 输出格式
            'subtitle_style': {         # 字幕样式
                'font_size': 24,
                'font_color': 'white',
                'font_name': 'Arial',
                'outline_color': 'black',
                'outline_width': 2
            }
        }
    
    def is_available(self):
        """检查FFmpeg合成器是否可用"""
        return FFMPEG_COMPOSER_AVAILABLE and self.ffmpeg_composer and self.ffmpeg_composer.is_available()

    def get_composer(self):
        """获取FFmpeg合成器"""
        if not self.is_available():
            raise Exception("FFmpeg不可用，请检查FFmpeg安装和配置")
        return self.ffmpeg_composer
    
    def get_install_info(self):
        """获取FFmpeg安装信息"""
        if self.is_available():
            return {
                'available': True,
                'composer': 'FFmpeg',
                'version': 'FFmpeg已安装并可用'
            }
        return {
            'available': False,
            'error': 'FFmpeg未安装或配置错误',
            'install_guide': '请下载FFmpeg并配置路径'
        }

    def get_media_duration(self, media_path):
        """
        获取媒体文件时长（使用FFmpeg）

        参数:
            media_path (str): 媒体文件路径

        返回:
            float: 媒体时长（秒）
        """
        try:
            if self.is_available():
                return self.ffmpeg_composer.get_media_duration(media_path)
            return 0.0
        except Exception:
            return 0.0

    def get_media_info(self, media_path):
        """
        获取媒体文件信息

        参数:
            media_path (str): 媒体文件路径

        返回:
            dict: 媒体信息
        """
        try:
            if self.is_available():
                # FFmpeg合成器暂时返回基本信息
                duration = self.get_media_duration(media_path)
                return {'duration': duration, 'path': media_path}
            return {}
        except Exception:
            return {}
    
    def compose_video_with_audio(self, video_path, audio_path, output_path,
                               subtitle_path=None, settings=None, progress_callback=None):
        """
        将音频与视频合成，可选添加字幕

        参数:
            video_path (str): 视频文件路径
            audio_path (str): 音频文件路径
            output_path (str): 输出视频路径
            subtitle_path (str): 字幕文件路径（可选）
            settings (dict): 合成设置
            progress_callback (function): 进度回调函数

        返回:
            bool: 是否成功
        """
        try:
            # 获取FFmpeg合成器
            composer = self.get_composer()

            if progress_callback:
                progress_callback("使用 FFmpeg 进行视频合成...")

            return composer.compose_video_with_audio(
                video_path, audio_path, output_path, subtitle_path, settings, progress_callback
            )

        except Exception as e:
            if progress_callback:
                progress_callback(f"视频合成失败: {str(e)}")
            return False

    def create_single_episode_video(self, loop_video, audio_path, subtitle_path, output_path, settings, progress_callback=None):
        """
        创建单集视频文件

        参数:
            loop_video (str): 循环视频路径
            audio_path (str): 音频文件路径
            subtitle_path (str): 字幕文件路径
            output_path (str): 输出文件路径
            settings (dict): 处理设置
            progress_callback (function): 进度回调函数

        返回:
            bool: 是否成功
        """
        try:
            # 获取FFmpeg合成器
            composer = self.get_composer()

            if progress_callback:
                progress_callback("使用 FFmpeg 创建视频...")

            # 使用FFmpeg合成器的compose_video_with_loop方法
            return composer.compose_video_with_loop(
                loop_video, audio_path, output_path, subtitle_path, settings, progress_callback
            )

        except Exception as e:
            if progress_callback:
                progress_callback(f"创建视频失败: {str(e)}")
            return False

    def batch_compose_videos(self, file_groups, output_dir, settings, progress_callback=None):
        """
        批量合成视频（支持多线程）

        参数:
            file_groups (list): 文件组列表，每个元素为 (video_path, audio_path, subtitle_path, output_name)
            output_dir (str): 输出目录
            settings (dict): 合成设置
            progress_callback (function): 进度回调函数

        返回:
            list: 处理结果列表
        """
        results = []
        total_files = len(file_groups)

        if progress_callback:
            progress_callback(f"开始批量处理 {total_files} 个文件...")

        # 检查是否启用并行处理
        enable_parallel = settings.get('enable_parallel', False)
        max_threads = settings.get('max_threads', 1)

        # 限制线程数在合理范围内
        max_threads = max(1, min(max_threads, 8))

        # 如果文件数少于线程数，调整线程数
        effective_threads = min(max_threads, total_files)

        if enable_parallel and effective_threads > 1 and total_files > 1:
            # 使用多线程处理
            if progress_callback:
                progress_callback(f"启用并行处理，使用 {effective_threads} 个线程处理 {total_files} 个文件")

            results = self._batch_compose_videos_threaded(
                file_groups, output_dir, settings, progress_callback, effective_threads
            )
        else:
            # 使用单线程处理
            reason = []
            if not enable_parallel:
                reason.append("并行处理未启用")
            if effective_threads <= 1:
                reason.append(f"有效线程数为 {effective_threads}")
            if total_files <= 1:
                reason.append(f"文件数量为 {total_files}")

            if progress_callback:
                progress_callback(f"使用单线程处理 ({', '.join(reason)})")

            results = self._batch_compose_videos_sequential(
                file_groups, output_dir, settings, progress_callback
            )

        return results

    def _batch_compose_videos_sequential(self, file_groups, output_dir, settings, progress_callback):
        """单线程顺序处理视频"""
        results = []
        total_files = len(file_groups)

        for i, (video_path, audio_path, subtitle_path, output_name) in enumerate(file_groups):
            if self.should_stop:
                break

            try:
                if progress_callback:
                    progress_callback({
                        "type": "file_progress",
                        "current_file": i + 1,
                        "total_files": total_files,
                        "file_name": output_name,
                        "message": f"正在处理文件 {i+1}/{total_files}: {output_name}"
                    })

                # 生成输出路径
                output_path = os.path.join(output_dir, f"{output_name}.{settings.get('format', 'mp4')}")

                # 合成视频
                success = self.compose_video_with_audio(
                    video_path, audio_path, output_path,
                    subtitle_path, settings, progress_callback
                )

                results.append({
                    'success': success,
                    'video_path': video_path,
                    'audio_path': audio_path,
                    'subtitle_path': subtitle_path,
                    'output_path': output_path,
                    'file_index': i
                })

                if progress_callback:
                    if success:
                        progress_callback(f"✓ 完成: {output_name}")
                    else:
                        progress_callback(f"✗ 失败: {output_name}")

            except Exception as e:
                if progress_callback:
                    progress_callback(f"处理文件 {output_name} 时出错: {str(e)}")

                results.append({
                    'success': False,
                    'video_path': video_path,
                    'audio_path': audio_path,
                    'subtitle_path': subtitle_path,
                    'output_path': None,
                    'file_index': i,
                    'error': str(e)
                })

        return results

    def _batch_compose_videos_threaded(self, file_groups, output_dir, settings, progress_callback, max_threads):
        """多线程并行处理视频"""
        results = []
        total_files = len(file_groups)
        completed_count = 0
        results_lock = threading.Lock()
        start_time = time.time()

        def process_single_video(file_info):
            """处理单个视频的工作函数"""
            nonlocal completed_count  # 声明使用外部变量

            i, (video_path, audio_path, subtitle_path, output_name) = file_info

            if self.should_stop:
                return None

            try:
                # 生成输出路径
                output_path = os.path.join(output_dir, f"{output_name}.{settings.get('format', 'mp4')}")

                # 创建线程安全的进度回调
                def thread_progress_callback(msg):
                    if progress_callback and not self.should_stop:
                        # 对于进度消息，添加文件标识
                        if isinstance(msg, str) and any(keyword in msg for keyword in ["导出进度:", "视频处理进度:", "处理进度:"]):
                            progress_callback(f"[{output_name}] {msg}")
                        else:
                            progress_callback(msg)

                # 合成视频
                success = self.compose_video_with_audio(
                    video_path, audio_path, output_path,
                    subtitle_path, settings, thread_progress_callback
                )

                result = {
                    'success': success,
                    'video_path': video_path,
                    'audio_path': audio_path,
                    'subtitle_path': subtitle_path,
                    'output_path': output_path,
                    'file_index': i,
                    'output_name': output_name
                }

                # 线程安全地更新进度
                with results_lock:
                    completed_count += 1

                    if progress_callback:
                        if success:
                            progress_callback(f"✓ 完成 ({completed_count}/{total_files}): {output_name}")
                        else:
                            progress_callback(f"✗ 失败 ({completed_count}/{total_files}): {output_name}")

                        # 更新总体进度
                        progress_callback({
                            "type": "overall_progress",
                            "completed": completed_count,
                            "total": total_files,
                            "percentage": (completed_count / total_files) * 100
                        })

                return result

            except Exception as e:
                with results_lock:
                    completed_count += 1

                    if progress_callback:
                        progress_callback(f"✗ 错误 ({completed_count}/{total_files}): {output_name} - {str(e)}")

                        # 更新总体进度
                        progress_callback({
                            "type": "overall_progress",
                            "completed": completed_count,
                            "total": total_files,
                            "percentage": (completed_count / total_files) * 100
                        })

                return {
                    'success': False,
                    'video_path': video_path,
                    'audio_path': audio_path,
                    'subtitle_path': subtitle_path,
                    'output_path': None,
                    'file_index': i,
                    'output_name': output_name,
                    'error': str(e)
                }

        # 使用线程池执行任务
        try:
            with ThreadPoolExecutor(max_workers=max_threads, thread_name_prefix="VideoComposer") as executor:
                # 提交所有任务
                future_to_file = {
                    executor.submit(process_single_video, (i, file_group)): i
                    for i, file_group in enumerate(file_groups)
                }

                if progress_callback:
                    progress_callback(f"已提交 {len(future_to_file)} 个任务到线程池")

                # 收集结果
                for future in as_completed(future_to_file):
                    if self.should_stop:
                        if progress_callback:
                            progress_callback("用户请求停止，正在取消剩余任务...")
                        # 取消未完成的任务
                        for f in future_to_file:
                            if not f.done():
                                f.cancel()
                        break

                    try:
                        result = future.result(timeout=300)  # 5分钟超时
                        if result is not None:
                            results.append(result)
                    except Exception as e:
                        file_index = future_to_file[future]
                        error_msg = f"线程执行错误 (文件 {file_index + 1}): {str(e)}"
                        if progress_callback:
                            progress_callback(error_msg)

                        # 添加错误结果
                        results.append({
                            'success': False,
                            'file_index': file_index,
                            'error': str(e),
                            'output_name': f"file_{file_index + 1}"
                        })

        except Exception as e:
            if progress_callback:
                progress_callback(f"线程池执行失败: {str(e)}")
            # 如果线程池失败，回退到单线程处理
            if progress_callback:
                progress_callback("回退到单线程处理...")
            return self._batch_compose_videos_sequential(
                file_groups, output_dir, settings, progress_callback
            )

        # 按文件索引排序结果
        results.sort(key=lambda x: x['file_index'])

        # 计算性能统计
        end_time = time.time()
        total_time = end_time - start_time

        if progress_callback:
            success_count = sum(1 for r in results if r.get('success', False))
            avg_time = total_time / len(results) if results else 0
            progress_callback(f"多线程处理完成: {success_count}/{len(results)} 个文件成功")
            progress_callback(f"总耗时: {total_time:.2f}秒, 平均每文件: {avg_time:.2f}秒")

        return results

        if progress_callback:
            successful = sum(1 for r in results if r['success'])
            progress_callback(f"批量处理完成: {successful}/{total_files} 个文件成功")

        return results

    def process_folders(self, settings, output_dir, progress_callback=None):
        """
        处理文件夹批量合成
        处理结构：视频素材/小说名称/媒体文件

        参数:
            settings (dict): 处理设置
            output_dir (str): 输出目录
            progress_callback (function): 进度回调函数

        返回:
            list: 处理结果列表
        """
        try:
            input_folder = settings['input_folder']
            if not os.path.exists(input_folder):
                raise Exception("输入文件夹不存在")

            # 扫描小说项目文件夹
            novel_folders = []
            for item in os.listdir(input_folder):
                item_path = os.path.join(input_folder, item)
                if os.path.isdir(item_path):
                    # 检查是否包含媒体文件
                    media_files = self.scan_media_files(item_path)
                    if media_files['has_media']:
                        novel_folders.append({
                            'path': item_path,
                            'name': item,
                            'media_files': media_files
                        })

            if not novel_folders:
                raise Exception("没有找到包含媒体文件的小说项目文件夹")

            if progress_callback:
                progress_callback(f"找到 {len(novel_folders)} 个小说项目")

            results = []
            total_novels = len(novel_folders)

            # 按顺序处理每个小说项目
            for i, novel_folder in enumerate(novel_folders):
                if self.should_stop:
                    break

                novel_name = novel_folder['name']
                novel_path = novel_folder['path']

                if progress_callback:
                    progress_callback({
                        "type": "file_progress",
                        "current_file": i + 1,
                        "total_files": total_novels,
                        "file_name": novel_name,
                        "message": f"正在处理小说项目 {i+1}/{total_novels}: {novel_name}"
                    })

                try:
                    # 处理单个小说项目
                    result = self.process_novel_project(
                        novel_folder, output_dir, settings, progress_callback
                    )
                    results.append(result)

                    if progress_callback:
                        progress_callback(f"小说项目 {novel_name} 处理完成")

                except Exception as e:
                    results.append({
                        'success': False,
                        'novel_name': novel_name,
                        'novel_path': novel_path,
                        'error': str(e)
                    })

                    if progress_callback:
                        progress_callback(f"小说项目 {novel_name} 处理失败: {str(e)}")

                if progress_callback:
                    progress_callback({
                        "type": "overall_progress",
                        "completed": i + 1,
                        "total": total_novels,
                        "percentage": ((i + 1) / total_novels) * 100
                    })

            return results

        except Exception as e:
            if progress_callback:
                progress_callback(f"处理失败: {str(e)}")
            raise e

    def scan_media_files(self, folder_path):
        """
        扫描文件夹中的媒体文件，按照指定命名格式分类

        文件命名格式：
        - 原视频：original.mp4 或 原视频.mp4
        - 循环视频：loop.mp4 或 循环.mp4
        - 音频文件：1.mp3, 2.mp3, 3.mp3...
        - 字幕文件：1.srt, 2.srt, 3.srt...

        参数:
            folder_path (str): 文件夹路径

        返回:
            dict: 媒体文件信息
        """
        media_files = {
            'original_video': None,      # 原视频
            'loop_video': None,          # 循环视频
            'episode_audios': {},        # 分集音频 {1: 'path', 2: 'path'}
            'episode_subtitles': {},     # 分集字幕 {1: 'path', 2: 'path'}
            'other_videos': [],          # 其他视频文件
            'other_audios': [],          # 其他音频文件
            'other_subtitles': [],       # 其他字幕文件
            'has_media': False
        }

        try:
            import re

            for file in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file)
                if os.path.isfile(file_path):
                    file_name = os.path.splitext(file)[0]
                    file_lower = file.lower()

                    # 视频文件处理
                    if file_lower.endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv')):
                        media_files['has_media'] = True

                        if file_name.lower() in ['original', '原视频']:
                            media_files['original_video'] = file_path
                        elif file_name.lower() in ['loop', '循环']:
                            media_files['loop_video'] = file_path
                        else:
                            media_files['other_videos'].append(file_path)

                    # 音频文件处理
                    elif file_lower.endswith(('.mp3', '.wav', '.aac', '.m4a', '.flac')):
                        media_files['has_media'] = True

                        # 提取文件名中的数字作为集数
                        episode_num = self.extract_episode_number(file_name)
                        if episode_num is not None:
                            media_files['episode_audios'][episode_num] = file_path
                        else:
                            media_files['other_audios'].append(file_path)

                    # 字幕文件处理
                    elif file_lower.endswith(('.srt', '.ass', '.vtt')):
                        # 提取文件名中的数字作为集数
                        episode_num = self.extract_episode_number(file_name)
                        if episode_num is not None:
                            media_files['episode_subtitles'][episode_num] = file_path
                        else:
                            media_files['other_subtitles'].append(file_path)

        except Exception as e:
            print(f"扫描媒体文件时出错: {e}")

        return media_files

    def extract_episode_number(self, filename):
        """
        从文件名中提取集数

        支持的格式：
        - 1, 2, 3... (纯数字)
        - 原视频_1, 西游记_2, abc_3... (前缀_数字)
        - 1_后缀, 2_abc... (数字_后缀)
        - 前缀_1_后缀 (前缀_数字_后缀)

        参数:
            filename (str): 文件名（不含扩展名）

        返回:
            int: 集数，如果没有找到数字则返回None
        """
        import re

        # 查找文件名中的所有数字
        numbers = re.findall(r'\d+', filename)

        if not numbers:
            return None

        # 如果只有一个数字，直接返回
        if len(numbers) == 1:
            return int(numbers[0])

        # 如果有多个数字，优先选择：
        # 1. 文件名开头的数字
        # 2. 下划线后的数字
        # 3. 最后一个数字

        # 检查是否以数字开头
        if re.match(r'^\d+', filename):
            return int(numbers[0])

        # 检查下划线后的数字
        underscore_match = re.search(r'_(\d+)', filename)
        if underscore_match:
            return int(underscore_match.group(1))

        # 返回最后一个数字
        return int(numbers[-1])

    def process_novel_project(self, novel_folder, output_dir, settings, progress_callback=None):
        """
        处理单个小说项目

        参数:
            novel_folder (dict): 小说项目信息
            output_dir (str): 输出目录
            settings (dict): 处理设置
            progress_callback (function): 进度回调函数

        返回:
            dict: 处理结果
        """
        try:
            novel_name = novel_folder['name']
            novel_path = novel_folder['path']
            media_files = novel_folder['media_files']

            if progress_callback:
                progress_callback(f"开始处理小说项目: {novel_name}")

            # 重新扫描媒体文件以获取详细分类
            detailed_media = self.scan_media_files(novel_path)

            # 验证必要文件
            validation_result = self.validate_media_files(detailed_media, novel_name)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'novel_name': novel_name,
                    'novel_path': novel_path,
                    'error': validation_result['error']
                }

            # 创建小说项目的输出目录
            novel_output_dir = os.path.join(output_dir, novel_name)
            if not os.path.exists(novel_output_dir):
                os.makedirs(novel_output_dir, exist_ok=True)

            # 根据合并功能开关决定处理方式
            enable_merge = settings.get('enable_merge', True)

            if not enable_merge:
                # 合并功能关闭：一个音频对应一个视频文件
                return self.create_individual_videos(
                    novel_name, detailed_media, novel_output_dir, settings, progress_callback
                )
            else:
                # 合并功能开启：根据合并模式处理
                merge_mode = settings.get('merge_mode', 'create_merged')

                if merge_mode == 'create_merged':
                    # 创建合并视频：将所有片段合并为单个视频
                    return self.create_merged_video(
                        novel_name, detailed_media, novel_output_dir, settings, progress_callback
                    )
                else:
                    # 合并原视频与第一集：将原视频与1号音频处理视频合并
                    return self.merge_with_first_episode(
                        novel_name, detailed_media, novel_output_dir, settings, progress_callback
                    )

        except Exception as e:
            return {
                'success': False,
                'novel_name': novel_folder['name'],
                'novel_path': novel_folder['path'],
                'error': str(e)
            }

    def validate_media_files(self, media_files, novel_name):
        """
        验证媒体文件是否符合要求

        参数:
            media_files (dict): 媒体文件信息
            novel_name (str): 小说名称

        返回:
            dict: 验证结果
        """
        errors = []

        # 检查必要文件
        if not media_files['loop_video']:
            errors.append("缺少循环视频文件 (loop.mp4 或 循环.mp4)")

        if not media_files['episode_audios']:
            errors.append("缺少分集音频文件 (1.mp3, 2.mp3, ...)")

        # 检查音频和字幕文件的对应关系
        audio_episodes = set(media_files['episode_audios'].keys())
        subtitle_episodes = set(media_files['episode_subtitles'].keys())

        if audio_episodes != subtitle_episodes:
            missing_subtitles = audio_episodes - subtitle_episodes
            missing_audios = subtitle_episodes - audio_episodes

            if missing_subtitles:
                errors.append(f"缺少字幕文件: {', '.join(f'{ep}.srt' for ep in sorted(missing_subtitles))}")
            if missing_audios:
                errors.append(f"缺少音频文件: {', '.join(f'{ep}.mp3' for ep in sorted(missing_audios))}")

        if errors:
            return {
                'valid': False,
                'error': f"小说项目 {novel_name} 文件不完整:\n" + "\n".join(f"- {error}" for error in errors)
            }

        return {
            'valid': True,
            'episode_count': len(audio_episodes),
            'has_original_video': media_files['original_video'] is not None
        }

    def create_merged_video(self, novel_name, media_files, output_dir, settings, progress_callback=None):
        """
        创建合并视频：将所有分集合并为单个视频

        参数:
            novel_name (str): 小说名称
            media_files (dict): 媒体文件信息
            output_dir (str): 输出目录
            settings (dict): 处理设置
            progress_callback (function): 进度回调函数

        返回:
            dict: 处理结果
        """
        try:
            if progress_callback:
                progress_callback(f"正在创建合并视频: {novel_name}")

            loop_video = media_files['loop_video']
            episode_audios = media_files['episode_audios']
            episode_subtitles = media_files['episode_subtitles']

            # 按集数排序
            sorted_episodes = sorted(episode_audios.keys())
            total_episodes = len(sorted_episodes)

            if progress_callback:
                progress_callback(f"准备合并 {total_episodes} 集")

            # 生成输出文件路径
            output_path = os.path.join(output_dir, f"{novel_name}_完整版.mp4")

            # 创建临时文件列表用于合并
            temp_videos = []

            try:
                for i, episode_num in enumerate(sorted_episodes):
                    if self.should_stop:
                        break

                    if progress_callback:
                        progress_callback(f"正在处理第 {episode_num} 集 ({i+1}/{total_episodes})")

                    audio_path = episode_audios[episode_num]
                    subtitle_path = episode_subtitles.get(episode_num)

                    # 为每一集创建临时视频
                    temp_video_path = self.create_episode_video(
                        loop_video, audio_path, subtitle_path, episode_num,
                        output_dir, settings, progress_callback
                    )

                    if temp_video_path:
                        temp_videos.append(temp_video_path)

                if not temp_videos:
                    raise Exception("没有成功创建任何分集视频")

                # 合并所有分集视频
                if progress_callback:
                    progress_callback(f"正在合并所有分集...")

                success = self.merge_videos(temp_videos, output_path, settings, progress_callback)

                if success:
                    if progress_callback:
                        progress_callback(f"合并完成: {novel_name}")

                    return {
                        'success': True,
                        'novel_name': novel_name,
                        'output_path': output_path,
                        'mode': 'create_merged',
                        'total_episodes': total_episodes
                    }
                else:
                    raise Exception("视频合并失败")

            finally:
                # 清理临时文件
                for temp_video in temp_videos:
                    try:
                        if os.path.exists(temp_video):
                            os.remove(temp_video)
                    except:
                        pass

        except Exception as e:
            return {
                'success': False,
                'novel_name': novel_name,
                'error': str(e),
                'mode': 'create_merged'
            }

    def merge_with_first_episode(self, novel_name, media_files, output_dir, settings, progress_callback=None):
        """
        合并原视频与第一集：将原视频与1号音频处理视频合并，其他集单独生成

        参数:
            novel_name (str): 小说名称
            media_files (dict): 媒体文件信息
            output_dir (str): 输出目录
            settings (dict): 处理设置
            progress_callback (function): 进度回调函数

        返回:
            dict: 处理结果
        """
        try:
            if progress_callback:
                progress_callback(f"开始合并原视频与第一集: {novel_name}")

            # 获取必要文件
            original_video = media_files.get('original_video')
            loop_video = media_files.get('loop_video')
            episode_audios = media_files.get('episode_audios', {})
            episode_subtitles = media_files.get('episode_subtitles', {})

            if not original_video:
                raise Exception("缺少原视频文件 (original.mp4 或 原视频.mp4)")

            if not loop_video:
                raise Exception("缺少循环视频文件 (loop.mp4 或 循环.mp4)")

            if not episode_audios:
                raise Exception("缺少分集音频文件")

            # 检查第一集音频
            if 1 not in episode_audios:
                raise Exception("缺少第一集音频文件 (1.mp3)")

            first_audio = episode_audios[1]
            first_subtitle = episode_subtitles.get(1)

            if progress_callback:
                progress_callback("正在创建第一集处理视频...")

            # 步骤1: 创建第一集处理视频（循环视频+第一集音频+字幕）
            first_episode_temp = os.path.join(output_dir, "temp_first_episode.mp4")

            # 创建第一集设置
            first_episode_settings = settings.copy()

            # 只有在启用自定义压缩时才设置压缩比例
            if settings.get('custom_compression', False):
                first_episode_settings['current_compression_ratio'] = settings.get('first_compression_ratio', 90)
            else:
                # 不使用自定义压缩
                first_episode_settings.pop('current_compression_ratio', None)

            success = self.create_single_episode_video(
                loop_video, first_audio, first_subtitle, first_episode_temp,
                first_episode_settings, progress_callback
            )

            if not success:
                raise Exception("创建第一集处理视频失败")

            if progress_callback:
                progress_callback("正在合并原视频与第一集...")

            # 步骤2: 合并原视频与第一集处理视频
            final_output = os.path.join(output_dir, f"{novel_name}_合并版.mp4")

            merge_success = self.merge_videos(
                [original_video, first_episode_temp],
                final_output,
                settings,
                progress_callback
            )

            if not merge_success:
                raise Exception("合并原视频与第一集失败")

            # 步骤3: 处理其他集（如果有的话）
            other_results = []
            sorted_episodes = sorted(episode_audios.keys())
            other_episodes = [ep for ep in sorted_episodes if ep != 1]

            if other_episodes:
                if progress_callback:
                    progress_callback(f"正在处理其他 {len(other_episodes)} 集...")

                for episode_num in other_episodes:
                    if self.should_stop:
                        break

                    try:
                        audio_path = episode_audios[episode_num]
                        subtitle_path = episode_subtitles.get(episode_num)

                        # 为其他集创建单独的视频
                        episode_output = os.path.join(output_dir, f"{novel_name}_第{episode_num}集.mp4")

                        # 使用其他集的压缩比例
                        episode_settings = settings.copy()
                        episode_settings['current_compression_ratio'] = settings.get('other_compression_ratio', 70)

                        episode_success = self.create_single_episode_video(
                            loop_video, audio_path, subtitle_path, episode_output,
                            episode_settings, progress_callback
                        )

                        other_results.append({
                            'episode': episode_num,
                            'success': episode_success,
                            'output_path': episode_output if episode_success else None
                        })

                        if progress_callback:
                            status = "完成" if episode_success else "失败"
                            progress_callback(f"第{episode_num}集: {status}")

                    except Exception as e:
                        other_results.append({
                            'episode': episode_num,
                            'success': False,
                            'error': str(e)
                        })

            # 清理临时文件
            try:
                if os.path.exists(first_episode_temp):
                    os.remove(first_episode_temp)
            except:
                pass

            if progress_callback:
                progress_callback(f"合并完成: {novel_name}")

            return {
                'success': True,
                'novel_name': novel_name,
                'main_output_path': final_output,
                'mode': 'merge_with_first',
                'total_episodes': len(sorted_episodes),
                'other_episodes': other_results
            }

        except Exception as e:
            # 清理临时文件
            try:
                first_episode_temp = os.path.join(output_dir, "temp_first_episode.mp4")
                if os.path.exists(first_episode_temp):
                    os.remove(first_episode_temp)
            except:
                pass

            return {
                'success': False,
                'novel_name': novel_name,
                'error': str(e),
                'mode': 'merge_with_first'
            }

    def create_episode_video(self, loop_video, audio_path, subtitle_path, episode_num, output_dir, settings, progress_callback=None):
        """
        创建单集视频（临时文件）

        参数:
            loop_video (str): 循环视频路径
            audio_path (str): 音频文件路径
            subtitle_path (str): 字幕文件路径
            episode_num (int): 集数
            output_dir (str): 输出目录
            settings (dict): 处理设置
            progress_callback (function): 进度回调函数

        返回:
            str: 临时视频文件路径
        """
        try:
            # 创建临时文件
            temp_video = os.path.join(output_dir, f"temp_episode_{episode_num}.mp4")

            # 创建集数设置
            episode_settings = settings.copy()

            # 只有在启用自定义压缩时才设置压缩比例
            if settings.get('custom_compression', False):
                # 根据集数选择压缩比例
                if episode_num == 1:
                    compression_ratio = settings.get('first_compression_ratio', 90)
                else:
                    compression_ratio = settings.get('other_compression_ratio', 70)

                episode_settings['current_compression_ratio'] = compression_ratio
            else:
                # 不使用自定义压缩，移除压缩比例设置
                episode_settings.pop('current_compression_ratio', None)

            # 使用FFmpeg创建视频
            success = self.create_single_episode_video(
                loop_video, audio_path, subtitle_path, temp_video, episode_settings, progress_callback
            )

            if success:
                return temp_video
            else:
                return None

        except Exception as e:
            if progress_callback:
                progress_callback(f"创建第{episode_num}集视频失败: {str(e)}")
            return None

    def merge_videos(self, video_paths, output_path, settings, progress_callback=None):
        """
        合并多个视频文件（使用FFmpeg）

        参数:
            video_paths (list): 视频文件路径列表
            output_path (str): 输出文件路径
            settings (dict): 处理设置
            progress_callback (function): 进度回调函数

        返回:
            bool: 是否成功
        """
        try:
            if not self.is_available():
                raise Exception("FFmpeg不可用，请检查FFmpeg安装和配置")

            if len(video_paths) < 2:
                raise Exception("需要至少两个视频文件进行合并")

            if progress_callback:
                progress_callback(f"正在合并 {len(video_paths)} 个视频文件...")

            # 获取FFmpeg合成器
            composer = self.get_composer()

            # 计算预期总时长
            total_expected_duration = 0
            for video_path in video_paths:
                try:
                    duration = composer.get_media_duration(video_path)
                    total_expected_duration += duration
                except Exception:
                    # 如果无法获取时长，跳过
                    pass

            # 直接使用优化的filter_complex方式（唯一方式）
            return self._merge_videos_with_filter_complex_optimized(
                video_paths, output_path, total_expected_duration,
                None, [], settings, progress_callback
            )

        except Exception as e:
            if progress_callback:
                progress_callback(f"合并视频失败: {str(e)}")
            return False

    def _merge_videos_with_concat(self, video_paths, output_path, concat_file, settings, progress_callback=None):
        """
        使用FFmpeg concat方式合并视频（带进度监控和调试信息）

        参数:
            video_paths (list): 视频文件路径列表
            output_path (str): 输出文件路径
            concat_file (str): concat文件路径
            settings (dict): 处理设置
            progress_callback (function): 进度回调函数

        返回:
            bool: 是否成功
        """
        try:
            composer = self.get_composer()
            ffmpeg_exe = composer._get_ffmpeg_executable()

            # 创建调试文件夹
            debug_dir = composer._create_debug_folder(output_path)
            debug_log = []

            # 记录合并信息
            debug_log.append("=== 视频合并调试日志 ===")
            debug_log.append(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            debug_log.append(f"输入视频数量: {len(video_paths)}")
            debug_log.append(f"输出路径: {output_path}")
            debug_log.append(f"调试文件夹: {debug_dir}")
            debug_log.append("")

            # 记录每个输入视频的信息
            total_expected_duration = 0
            for i, video_path in enumerate(video_paths):
                try:
                    duration = composer.get_media_duration(video_path)
                    total_expected_duration += duration
                    debug_log.append(f"输入视频 {i+1}: {os.path.basename(video_path)} - {duration:.2f}秒")
                except Exception as e:
                    debug_log.append(f"输入视频 {i+1}: {os.path.basename(video_path)} - 无法获取时长: {e}")

            debug_log.append(f"预期总时长: {total_expected_duration:.2f}秒")
            debug_log.append("")

            if progress_callback:
                progress_callback(f"正在使用concat方式合并 {len(video_paths)} 个视频...")
                progress_callback(f"预期总时长: {total_expected_duration:.2f}秒")

            # 尝试多种时间戳修复方案，避免重新编码
            debug_log.append("=== 尝试时间戳修复方案 ===")
            if progress_callback:
                progress_callback("尝试修复时间戳问题，保持快速处理...")

            # 方案1: 使用时间戳重置参数
            success = self._merge_with_timestamp_fix_v1(video_paths, output_path, concat_file, total_expected_duration, debug_dir, debug_log, settings, progress_callback)
            if success:
                return True

            # 方案2: 使用更强的时间戳修复
            success = self._merge_with_timestamp_fix_v2(video_paths, output_path, concat_file, total_expected_duration, debug_dir, debug_log, settings, progress_callback)
            if success:
                return True

            # 方案3: 最后才使用重新编码
            if progress_callback:
                progress_callback("时间戳修复失败，使用重新编码方式...")
            return self._merge_videos_with_reencoding_v2(video_paths, output_path, concat_file, total_expected_duration, debug_dir, debug_log, settings, progress_callback)

        except Exception as e:
            debug_log.append(f"❌ 合并过程出错: {e}")
            composer._save_debug_log(debug_dir, debug_log)
            if progress_callback:
                progress_callback(f"合并失败: {str(e)}")
            return False

        finally:
            # 清理进度文件
            try:
                os.unlink(progress_file.name)
            except:
                pass

    def _merge_videos_with_reencoding_v2(self, video_paths, output_path, concat_file, total_expected_duration, debug_dir, debug_log, settings, progress_callback=None):
        """
        使用重新编码方式合并视频（带调试信息）
        """
        try:
            composer = self.get_composer()
            ffmpeg_exe = composer._get_ffmpeg_executable()

            debug_log.append("\n=== 重新编码合并 ===")

            # 创建临时进度文件
            import tempfile
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 获取优化的编码器设置
            codec, ffmpeg_params = composer._get_optimal_encoder_settings(settings or {})

            # 获取并行处理设置
            enable_parallel = settings.get('enable_parallel', True) if settings else True
            max_threads = settings.get('max_threads', 8) if settings else 8

            # 使用concat + 重新编码，添加时间戳修复参数和性能优化
            cmd_reencode = [
                ffmpeg_exe, '-y',
                '-progress', progress_file.name,
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file,
                '-c:v', codec,  # 使用优化的编码器（可能是硬件加速）
                '-pix_fmt', 'yuv420p',
                '-c:a', 'aac',
                '-b:a', '128k',
                '-ar', '44100',
                '-ac', '2',
                '-avoid_negative_ts', 'make_zero',  # 修复负时间戳
                '-fflags', '+genpts',  # 重新生成时间戳
                '-vsync', 'cfr',  # 恒定帧率
                '-async', '1',  # 音频同步
                '-max_muxing_queue_size', '1024',  # 增加缓冲区
                '-movflags', '+faststart'
            ]

            # 添加编码器优化参数
            cmd_reencode.extend(ffmpeg_params)

            # 如果是软件编码器，添加多线程支持
            if 'libx264' in codec and enable_parallel and max_threads > 0:
                cmd_reencode.extend(['-threads', str(max_threads)])

            cmd_reencode.append(output_path)

            debug_log.append(f"重新编码命令: {' '.join(cmd_reencode)}")

            if progress_callback:
                progress_callback("正在重新编码合并...")

            # 启动FFmpeg进程（隐藏窗口）
            import subprocess
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd_reencode, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd_reencode, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 监控进度
            if progress_callback:
                composer._monitor_ffmpeg_progress(progress_file.name, total_expected_duration, progress_callback, "重编码合并")

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 保存命令和输出
            composer._save_ffmpeg_command(debug_dir, "merge_reencode", cmd_reencode, stdout, stderr, process.returncode)

            if process.returncode == 0:
                # 检查输出文件时长
                try:
                    output_duration = composer.get_media_duration(output_path)
                    debug_log.append(f"✅ 重新编码合并成功")
                    debug_log.append(f"输出时长: {output_duration:.2f}秒")
                    debug_log.append(f"预期时长: {total_expected_duration:.2f}秒")
                    debug_log.append(f"时长差异: {abs(output_duration - total_expected_duration):.2f}秒")

                    composer._save_debug_log(debug_dir, debug_log)

                    if progress_callback:
                        progress_callback(f"✅ 重新编码合并完成，时长: {output_duration:.2f}秒")

                    return True
                except Exception as e:
                    debug_log.append(f"❌ 无法检查输出文件: {e}")

            else:
                debug_log.append(f"❌ 重新编码也失败: {stderr}")
                if progress_callback:
                    progress_callback("concat方式失败，尝试filter_complex方式...")

                # 最后尝试使用filter_complex方式
                return self._merge_videos_with_filter_complex(video_paths, output_path, total_expected_duration, debug_dir, debug_log, settings, progress_callback)

        except Exception as e:
            debug_log.append(f"❌ 重新编码过程出错: {e}")
            composer._save_debug_log(debug_dir, debug_log)
            if progress_callback:
                progress_callback(f"重新编码失败: {str(e)}")
            return False

        finally:
            # 清理进度文件
            try:
                os.unlink(progress_file.name)
            except:
                pass

    def _merge_with_timestamp_fix_v1(self, video_paths, output_path, concat_file, total_expected_duration, debug_dir, debug_log, settings, progress_callback=None):
        """
        时间戳修复方案1: 使用基本的时间戳修复参数
        """
        try:
            composer = self.get_composer()
            ffmpeg_exe = composer._get_ffmpeg_executable()

            debug_log.append("\n=== 时间戳修复方案1 ===")

            # 创建临时进度文件
            import tempfile
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 方案1: 基本时间戳修复
            cmd_fix1 = [
                ffmpeg_exe, '-y',
                '-progress', progress_file.name,
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file,
                '-c', 'copy',
                '-avoid_negative_ts', 'make_zero',  # 修复负时间戳
                '-fflags', '+genpts',  # 重新生成PTS
                '-max_muxing_queue_size', '1024',  # 增加缓冲区
                output_path
            ]

            debug_log.append(f"方案1命令: {' '.join(cmd_fix1)}")

            if progress_callback:
                progress_callback("尝试基本时间戳修复...")

            # 启动FFmpeg进程（隐藏窗口）
            import subprocess
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd_fix1, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd_fix1, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 监控进度
            if progress_callback:
                composer._monitor_ffmpeg_progress(progress_file.name, total_expected_duration, progress_callback, "修复合并v1")

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 保存命令和输出
            composer._save_ffmpeg_command(debug_dir, "merge_fix_v1", cmd_fix1, stdout, stderr, process.returncode)

            if process.returncode == 0:
                # 检查输出文件时长
                try:
                    output_duration = composer.get_media_duration(output_path)
                    time_diff = abs(output_duration - total_expected_duration)

                    debug_log.append(f"✅ 方案1成功")
                    debug_log.append(f"输出时长: {output_duration:.2f}秒")
                    debug_log.append(f"预期时长: {total_expected_duration:.2f}秒")
                    debug_log.append(f"时长差异: {time_diff:.2f}秒")

                    # 如果时长差异小于5秒，认为成功
                    if time_diff < 5.0:
                        composer._save_debug_log(debug_dir, debug_log)
                        if progress_callback:
                            progress_callback(f"✅ 时间戳修复成功，时长: {output_duration:.2f}秒")
                        return True
                    else:
                        debug_log.append(f"⚠️ 时长差异过大，尝试下一个方案")

                except Exception as e:
                    debug_log.append(f"❌ 无法检查输出文件: {e}")

            else:
                debug_log.append(f"❌ 方案1失败: {stderr}")

            return False

        except Exception as e:
            debug_log.append(f"❌ 方案1过程出错: {e}")
            return False

        finally:
            # 清理进度文件
            try:
                os.unlink(progress_file.name)
            except:
                pass

    def _merge_with_timestamp_fix_v2(self, video_paths, output_path, concat_file, total_expected_duration, debug_dir, debug_log, settings, progress_callback=None):
        """
        时间戳修复方案2: 使用更强的时间戳修复参数
        """
        try:
            composer = self.get_composer()
            ffmpeg_exe = composer._get_ffmpeg_executable()

            debug_log.append("\n=== 时间戳修复方案2 ===")

            # 创建临时进度文件
            import tempfile
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 方案2: 更强的时间戳修复
            cmd_fix2 = [
                ffmpeg_exe, '-y',
                '-progress', progress_file.name,
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file,
                '-c', 'copy',
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts+igndts',  # 忽略DTS，重新生成PTS
                '-max_muxing_queue_size', '2048',
                '-max_interleave_delta', '0',  # 禁用交错
                '-bsf:v', 'h264_mp4toannexb,h264_metadata=tick_rate=90000',  # 修复H264时间戳
                output_path
            ]

            debug_log.append(f"方案2命令: {' '.join(cmd_fix2)}")

            if progress_callback:
                progress_callback("尝试强化时间戳修复...")

            # 启动FFmpeg进程（隐藏窗口）
            import subprocess
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd_fix2, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd_fix2, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 监控进度
            if progress_callback:
                composer._monitor_ffmpeg_progress(progress_file.name, total_expected_duration, progress_callback, "修复合并v2")

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 保存命令和输出
            composer._save_ffmpeg_command(debug_dir, "merge_fix_v2", cmd_fix2, stdout, stderr, process.returncode)

            if process.returncode == 0:
                # 检查输出文件时长
                try:
                    output_duration = composer.get_media_duration(output_path)
                    time_diff = abs(output_duration - total_expected_duration)

                    debug_log.append(f"✅ 方案2成功")
                    debug_log.append(f"输出时长: {output_duration:.2f}秒")
                    debug_log.append(f"预期时长: {total_expected_duration:.2f}秒")
                    debug_log.append(f"时长差异: {time_diff:.2f}秒")

                    # 如果时长差异小于5秒，认为成功
                    if time_diff < 5.0:
                        composer._save_debug_log(debug_dir, debug_log)
                        if progress_callback:
                            progress_callback(f"✅ 强化时间戳修复成功，时长: {output_duration:.2f}秒")
                        return True
                    else:
                        debug_log.append(f"⚠️ 时长差异仍然过大")

                except Exception as e:
                    debug_log.append(f"❌ 无法检查输出文件: {e}")

            else:
                debug_log.append(f"❌ 方案2失败: {stderr}")

            return False

        except Exception as e:
            debug_log.append(f"❌ 方案2过程出错: {e}")
            return False

        finally:
            # 清理进度文件
            try:
                os.unlink(progress_file.name)
            except:
                pass

    def _merge_videos_with_filter_complex(self, video_paths, output_path, total_expected_duration, debug_dir, debug_log, settings, progress_callback=None):
        """
        使用filter_complex方式合并视频（最强兼容性）
        """
        try:
            composer = self.get_composer()
            ffmpeg_exe = composer._get_ffmpeg_executable()

            debug_log.append("\n=== filter_complex合并 ===")

            # 创建临时进度文件
            import tempfile
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 构建filter_complex命令，统一所有视频的格式
            filter_parts = []
            input_args = []

            # 添加所有输入文件
            for i, video_path in enumerate(video_paths):
                input_args.extend(['-i', video_path])
                # 标准化每个输入：统一分辨率、帧率、像素格式
                filter_parts.append(f'[{i}:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,fps=30,format=yuv420p[v{i}]')
                filter_parts.append(f'[{i}:a]aresample=44100,aformat=sample_fmts=fltp:channel_layouts=stereo[a{i}]')

            # 合并所有视频和音频流
            video_inputs = ''.join([f'[v{i}]' for i in range(len(video_paths))])
            audio_inputs = ''.join([f'[a{i}]' for i in range(len(video_paths))])

            filter_parts.append(f'{video_inputs}concat=n={len(video_paths)}:v=1:a=0[outv]')
            filter_parts.append(f'{audio_inputs}concat=n={len(video_paths)}:v=0:a=1[outa]')

            filter_complex = ';'.join(filter_parts)

            # 获取优化的编码器设置
            codec, ffmpeg_params = composer._get_optimal_encoder_settings(settings or {})

            # 获取并行处理设置
            enable_parallel = settings.get('enable_parallel', True) if settings else True
            max_threads = settings.get('max_threads', 8) if settings else 8

            # 构建优化的完整命令
            cmd_filter = [ffmpeg_exe, '-y', '-progress', progress_file.name] + input_args + [
                '-filter_complex', filter_complex,
                '-map', '[outv]',
                '-map', '[outa]',
                '-c:v', codec,  # 使用优化的编码器（可能是硬件加速）
                '-c:a', 'aac',
                '-b:a', '128k',
                '-movflags', '+faststart'
            ]

            # 添加编码器优化参数
            cmd_filter.extend(ffmpeg_params)

            # 如果是软件编码器，添加多线程支持
            if 'libx264' in codec and enable_parallel and max_threads > 0:
                cmd_filter.extend(['-threads', str(max_threads)])

            cmd_filter.append(output_path)

            debug_log.append(f"filter_complex命令: {' '.join(cmd_filter)}")

            if progress_callback:
                progress_callback("正在使用filter_complex合并...")

            # 启动FFmpeg进程（隐藏窗口）
            import subprocess
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd_filter, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd_filter, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 监控进度
            if progress_callback:
                composer._monitor_ffmpeg_progress(progress_file.name, total_expected_duration, progress_callback, "filter合并")

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 保存命令和输出
            composer._save_ffmpeg_command(debug_dir, "merge_filter", cmd_filter, stdout, stderr, process.returncode)

            if process.returncode == 0:
                # 检查输出文件时长
                try:
                    output_duration = composer.get_media_duration(output_path)
                    debug_log.append(f"✅ filter_complex合并成功")
                    debug_log.append(f"输出时长: {output_duration:.2f}秒")
                    debug_log.append(f"预期时长: {total_expected_duration:.2f}秒")
                    debug_log.append(f"时长差异: {abs(output_duration - total_expected_duration):.2f}秒")

                    composer._save_debug_log(debug_dir, debug_log)

                    if progress_callback:
                        progress_callback(f"✅ filter_complex合并完成，时长: {output_duration:.2f}秒")

                    return True
                except Exception as e:
                    debug_log.append(f"❌ 无法检查输出文件: {e}")

            else:
                debug_log.append(f"❌ filter_complex也失败: {stderr}")
                composer._save_debug_log(debug_dir, debug_log)
                if progress_callback:
                    progress_callback(f"所有合并方式都失败: {stderr}")
                return False

        except Exception as e:
            debug_log.append(f"❌ filter_complex过程出错: {e}")
            composer._save_debug_log(debug_dir, debug_log)
            if progress_callback:
                progress_callback(f"filter_complex失败: {str(e)}")

            # 尝试分段并行处理方式
            if progress_callback:
                progress_callback("尝试分段并行处理...")
            return self._merge_videos_parallel_segments(video_files, output_path, settings, progress_callback, debug_log)

        except ImportError as e:
            debug_log.append(f"❌ 导入错误: {e}")
            if progress_callback:
                progress_callback(f"导入错误: {str(e)}")
            return False

        finally:
            # 清理进度文件
            try:
                os.unlink(progress_file.name)
            except:
                pass

    def _merge_videos_with_filter_complex_optimized(self, video_paths, output_path, total_expected_duration, debug_dir, debug_log, settings, progress_callback=None):
        """
        使用优化的filter_complex方式合并视频（支持多线程和硬件加速）
        """
        try:
            composer = self.get_composer()
            ffmpeg_exe = composer._get_ffmpeg_executable()

            if progress_callback:
                progress_callback("正在使用filter_complex合并...")

            # 构建输入参数
            input_args = []
            for video_path in video_paths:
                input_args.extend(['-i', video_path])

            # 构建filter_complex滤镜
            if len(video_paths) == 2:
                # 两个视频的简单合并
                filter_complex = f"[0:v][0:a][1:v][1:a]concat=n=2:v=1:a=1[outv][outa]"
            else:
                # 多个视频的合并
                video_inputs = "".join([f"[{i}:v][{i}:a]" for i in range(len(video_paths))])
                filter_complex = f"{video_inputs}concat=n={len(video_paths)}:v=1:a=1[outv][outa]"

            # 获取优化的编码器设置
            codec, ffmpeg_params = composer._get_optimal_encoder_settings(settings or {})

            # 获取并行处理设置
            enable_parallel = settings.get('enable_parallel', True) if settings else True
            max_threads = settings.get('max_threads', 8) if settings else 8

            # 创建临时进度文件
            import tempfile
            progress_file = tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False)
            progress_file.close()

            # 构建优化的完整命令
            cmd_filter = [ffmpeg_exe, '-y', '-progress', progress_file.name] + input_args + [
                '-filter_complex', filter_complex,
                '-map', '[outv]',
                '-map', '[outa]',
                '-c:v', codec,  # 使用优化的编码器（可能是硬件加速）
                '-c:a', 'aac',
                '-b:a', '128k',
                '-movflags', '+faststart'
            ]

            # 添加编码器优化参数
            cmd_filter.extend(ffmpeg_params)

            # 多线程支持
            if enable_parallel and max_threads > 0:
                if 'libx264' in codec:
                    # 软件编码器多线程
                    cmd_filter.extend(['-threads', str(max_threads)])
                elif any(hw in codec for hw in ['nvenc', 'qsv', 'amf']):
                    # 硬件编码器并行处理
                    cmd_filter.extend(['-threads', str(min(max_threads, 4))])  # 硬件编码器线程数限制

            cmd_filter.append(output_path)

            # 启动FFmpeg进程（隐藏窗口）
            import subprocess
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd_filter, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd_filter, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 监控进度
            if progress_callback and total_expected_duration > 0:
                composer._monitor_ffmpeg_progress(progress_file.name, total_expected_duration, progress_callback, "filter合并")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode == 0:
                if progress_callback:
                    progress_callback("✅ filter_complex合并完成")
                return True
            else:
                if progress_callback:
                    progress_callback(f"filter_complex合并失败: {stderr}")
                return False

        except Exception as e:
            if progress_callback:
                progress_callback(f"filter_complex失败: {str(e)}")
            return False

        finally:
            # 清理进度文件
            try:
                os.unlink(progress_file.name)
            except:
                pass

    def _merge_videos_parallel_segments(self, video_files, output_path, settings, progress_callback=None, debug_log=None):
        """
        分段并行处理视频合并（最后的备用方案）
        将大视频分成小段，并行处理后再合并
        """
        if debug_log is None:
            debug_log = []

        try:
            composer = self.get_composer()
            ffmpeg_exe = composer._get_ffmpeg_executable()

            if progress_callback:
                progress_callback("开始分段并行处理...")

            debug_log.append("\n=== 分段并行处理 ===")

            # 获取视频信息
            total_duration = 0
            for video_file in video_files:
                try:
                    duration = composer.get_media_duration(video_file)
                    total_duration += duration
                    debug_log.append(f"视频 {os.path.basename(video_file)}: {duration:.2f}秒")
                except Exception as e:
                    debug_log.append(f"获取时长失败 {video_file}: {e}")

            debug_log.append(f"总时长: {total_duration:.2f}秒")

            # 计算分段策略
            segment_duration = 300  # 每段5分钟
            num_segments = max(1, int(total_duration / segment_duration))

            if num_segments == 1:
                # 如果只有一段，直接使用简单合并
                if progress_callback:
                    progress_callback("视频较短，使用简单合并...")
                return self._simple_video_merge(video_files, output_path, settings, progress_callback, debug_log)

            debug_log.append(f"分为 {num_segments} 段处理")

            # 获取并行处理设置
            enable_parallel = settings.get('enable_parallel', True) if settings else True
            max_threads = min(settings.get('max_threads', 4) if settings else 4, num_segments)

            if not enable_parallel:
                max_threads = 1

            debug_log.append(f"使用 {max_threads} 个线程")

            # 创建临时目录
            import tempfile
            temp_dir = tempfile.mkdtemp(prefix="video_segments_")
            segment_files = []

            try:
                # 分段处理
                import threading
                import queue

                def process_segment(segment_info):
                    seg_idx, start_time, duration, output_seg = segment_info
                    try:
                        # 获取优化的编码器设置
                        codec, ffmpeg_params = composer._get_optimal_encoder_settings(settings or {})

                        cmd = [
                            ffmpeg_exe, '-y',
                            '-ss', str(start_time),
                            '-t', str(duration),
                            '-i', video_files[0] if video_files else '',
                            '-c:v', codec,
                            '-c:a', 'copy',  # 音频直接复制，提高速度
                            '-avoid_negative_ts', 'make_zero',
                            output_seg
                        ]

                        # 添加编码器参数
                        cmd[7:7] = ffmpeg_params  # 在输出文件前插入参数

                        import subprocess
                        import platform
                        if platform.system() == "Windows":
                            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                                     creationflags=subprocess.CREATE_NO_WINDOW)
                        else:
                            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                        stdout, stderr = process.communicate()

                        if process.returncode == 0:
                            return (seg_idx, output_seg, True, "")
                        else:
                            return (seg_idx, output_seg, False, stderr)

                    except Exception as e:
                        return (seg_idx, output_seg, False, str(e))

                # 创建分段任务
                segment_tasks = []
                current_time = 0

                for i in range(num_segments):
                    if i == num_segments - 1:
                        # 最后一段包含剩余所有时间
                        seg_duration = total_duration - current_time
                    else:
                        seg_duration = segment_duration

                    output_seg = os.path.join(temp_dir, f"segment_{i:03d}.mp4")
                    segment_tasks.append((i, current_time, seg_duration, output_seg))
                    current_time += seg_duration

                # 并行处理分段
                if progress_callback:
                    progress_callback(f"并行处理 {num_segments} 个分段...")

                results = []
                if max_threads > 1:
                    # 多线程处理
                    with concurrent.futures.ThreadPoolExecutor(max_workers=max_threads) as executor:
                        future_to_segment = {executor.submit(process_segment, task): task for task in segment_tasks}

                        for future in concurrent.futures.as_completed(future_to_segment):
                            result = future.result()
                            results.append(result)

                            if progress_callback:
                                completed = len(results)
                                progress_callback(f"分段处理进度: {completed}/{num_segments}")
                else:
                    # 单线程处理
                    for i, task in enumerate(segment_tasks):
                        result = process_segment(task)
                        results.append(result)

                        if progress_callback:
                            progress_callback(f"分段处理进度: {i+1}/{num_segments}")

                # 检查处理结果
                successful_segments = [r for r in results if r[2]]  # r[2] is success flag

                if len(successful_segments) != num_segments:
                    failed_segments = [r for r in results if not r[2]]
                    debug_log.append(f"❌ {len(failed_segments)} 个分段处理失败")
                    for seg_idx, _, _, error in failed_segments:
                        debug_log.append(f"分段 {seg_idx} 失败: {error}")
                    return False

                # 合并分段
                segment_files = [r[1] for r in sorted(successful_segments, key=lambda x: x[0])]

                if progress_callback:
                    progress_callback("合并处理后的分段...")

                # 使用简单concat合并分段
                return self._simple_concat_merge(segment_files, output_path, settings, progress_callback, debug_log)

            finally:
                # 清理临时文件
                for seg_file in segment_files:
                    try:
                        if os.path.exists(seg_file):
                            os.unlink(seg_file)
                    except:
                        pass
                try:
                    os.rmdir(temp_dir)
                except:
                    pass

        except Exception as e:
            debug_log.append(f"❌ 分段并行处理失败: {e}")
            if progress_callback:
                progress_callback(f"分段并行处理失败: {str(e)}")
            return False

    def _simple_video_merge(self, video_files, output_path, settings, progress_callback=None, debug_log=None):
        """简单视频合并（最基础的方案）"""
        if debug_log is None:
            debug_log = []

        try:
            composer = self.get_composer()
            ffmpeg_exe = composer._get_ffmpeg_executable()

            if progress_callback:
                progress_callback("使用最简单的合并方式...")

            # 创建concat文件
            import tempfile
            concat_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')

            try:
                for video_file in video_files:
                    abs_path = os.path.abspath(video_file).replace('\\', '/')
                    concat_file.write(f"file '{abs_path}'\n")
                concat_file.close()

                # 最简单的concat命令
                cmd = [
                    ffmpeg_exe, '-y',
                    '-f', 'concat',
                    '-safe', '0',
                    '-i', concat_file.name,
                    '-c', 'copy',  # 直接复制，不重编码
                    output_path
                ]

                import subprocess
                import platform
                if platform.system() == "Windows":
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                             creationflags=subprocess.CREATE_NO_WINDOW)
                else:
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                stdout, stderr = process.communicate()

                if process.returncode == 0:
                    debug_log.append("✅ 简单合并成功")
                    if progress_callback:
                        progress_callback("简单合并完成")
                    return True
                else:
                    debug_log.append(f"❌ 简单合并失败: {stderr}")
                    return False

            finally:
                try:
                    os.unlink(concat_file.name)
                except:
                    pass

        except Exception as e:
            debug_log.append(f"❌ 简单合并出错: {e}")
            return False

    def _simple_concat_merge(self, video_files, output_path, settings, progress_callback=None, debug_log=None):
        """简单concat合并（用于合并分段）"""
        return self._simple_video_merge(video_files, output_path, settings, progress_callback, debug_log)

    def _merge_videos_with_reencoding(self, video_paths, output_path, settings, progress_callback=None):
        """
        使用重新编码方式合并视频（兼容性更好）

        参数:
            video_paths (list): 视频文件路径列表
            output_path (str): 输出文件路径
            settings (dict): 处理设置
            progress_callback (function): 进度回调函数

        返回:
            bool: 是否成功
        """
        try:
            composer = self.get_composer()
            ffmpeg_exe = composer._get_ffmpeg_executable()

            # 获取编码器设置
            codec, ffmpeg_params = composer._get_optimal_encoder_settings(settings or {})

            # 构建filter_complex命令，统一所有视频的格式
            filter_parts = []
            input_args = []

            # 添加所有输入文件
            for i, video_path in enumerate(video_paths):
                input_args.extend(['-i', video_path])
                # 标准化每个输入：统一分辨率、帧率、像素格式
                filter_parts.append(f'[{i}:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,fps=30,format=yuv420p[v{i}]')
                filter_parts.append(f'[{i}:a]aresample=44100,aformat=sample_fmts=fltp:channel_layouts=stereo[a{i}]')

            # 合并所有视频和音频流
            video_inputs = ''.join([f'[v{i}]' for i in range(len(video_paths))])
            audio_inputs = ''.join([f'[a{i}]' for i in range(len(video_paths))])

            filter_parts.append(f'{video_inputs}concat=n={len(video_paths)}:v=1:a=0[outv]')
            filter_parts.append(f'{audio_inputs}concat=n={len(video_paths)}:v=0:a=1[outa]')

            filter_complex = ';'.join(filter_parts)

            # 构建完整命令
            cmd = [ffmpeg_exe, '-y'] + input_args + [
                '-filter_complex', filter_complex,
                '-map', '[outv]',
                '-map', '[outa]',
                '-c:v', codec,
                '-c:a', 'aac',
                '-b:a', '128k'
            ] + ffmpeg_params + [output_path]

            if progress_callback:
                progress_callback("正在重新编码合并视频...")

            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')

            if result.returncode != 0:
                raise Exception(f"重新编码合并失败: {result.stderr}")

            if progress_callback:
                progress_callback("✅ 兼容性合并完成")

            return True

        except Exception as e:
            if progress_callback:
                progress_callback(f"重新编码合并失败: {str(e)}")
            return False

    def stop_processing(self):
        """停止处理"""
        self.should_stop = True

        # 终止所有视频FFmpeg进程
        print("正在终止所有视频FFmpeg进程...")
        video_ffmpeg_manager.kill_all_video_ffmpeg_processes()

    def cleanup_on_exit(self):
        """退出时清理资源"""
        print("清理视频合成资源...")
        self.stop_processing()
        if self.ffmpeg_composer:
            self.ffmpeg_composer.should_stop = True

    def reset_stop_flag(self):
        """重置停止标志"""
        self.should_stop = False
        if self.ffmpeg_composer:
            self.ffmpeg_composer.should_stop = False

    def _color_to_hex(self, color_name):
        """将颜色名称转换为十六进制"""
        color_map = {
            'white': 'FFFFFF',
            'black': '000000',
            'red': 'FF0000',
            'green': '00FF00',
            'blue': '0000FF',
            'yellow': 'FFFF00',
            'cyan': '00FFFF',
            'magenta': 'FF00FF'
        }
        return color_map.get(color_name.lower(), 'FFFFFF')

    def _parse_subtitle_style(self, style_str):
        """解析字幕样式字符串"""
        try:
            parts = style_str.split()
            font_name = parts[0] if parts else '微软雅黑'
            
            # 提取字体大小
            font_size = 22
            for part in parts:
                if 'pt' in part:
                    font_size = int(part.replace('pt', ''))
                    break
            
            font_color = 'white'
            if '白色' in style_str or 'white' in style_str.lower():
                font_color = 'white'
            elif '黑色' in style_str or 'black' in style_str.lower():
                font_color = 'black'
            
            return font_name, font_size, font_color
            
        except Exception:
            return '微软雅黑', 22, 'white'

    def create_individual_videos(self, novel_name, media_files, output_dir, settings, progress_callback=None):
        """
        创建独立视频：一个音频对应一个视频文件

        参数:
            novel_name (str): 小说名称
            media_files (dict): 媒体文件信息
            output_dir (str): 输出目录
            settings (dict): 处理设置
            progress_callback (function): 进度回调函数

        返回:
            dict: 处理结果
        """
        try:
            if progress_callback:
                progress_callback(f"开始创建独立视频文件: {novel_name}")

            # 获取文件列表
            video_files = media_files.get('video_files', [])
            audio_files = media_files.get('audio_files', [])
            subtitle_files = media_files.get('subtitle_files', [])

            if not video_files:
                return {
                    'success': False,
                    'novel_name': novel_name,
                    'error': '没有找到视频文件'
                }

            if not audio_files:
                return {
                    'success': False,
                    'novel_name': novel_name,
                    'error': '没有找到音频文件'
                }

            # 使用第一个视频文件作为背景视频
            background_video = video_files[0]

            if progress_callback:
                progress_callback(f"使用背景视频: {os.path.basename(background_video)}")
                progress_callback(f"找到 {len(audio_files)} 个音频文件")

            # 创建FFmpeg合成器
            from .ffmpeg_composer import FFmpegComposer
            composer = FFmpegComposer()

            # 处理结果列表
            individual_results = []
            total_audio = len(audio_files)

            # 为每个音频文件创建对应的视频
            for i, audio_file in enumerate(audio_files):
                if self.should_stop:
                    break

                audio_name = os.path.basename(audio_file)
                audio_number = self.extract_number_from_filename(audio_name)

                if progress_callback:
                    progress_callback(f"处理音频 {i+1}/{total_audio}: {audio_name}")

                # 查找对应的字幕文件
                subtitle_file = None
                if settings.get('enable_subtitle', False):
                    subtitle_file = self.find_matching_subtitle(audio_file, subtitle_files)

                # 生成输出文件名
                output_name = f"{novel_name}_{audio_number:02d}.{settings.get('format', 'mp4')}"
                output_path = os.path.join(output_dir, output_name)

                try:
                    # 创建单个视频
                    success = composer.create_video_with_audio(
                        video_path=background_video,
                        audio_path=audio_file,
                        subtitle_path=subtitle_file,
                        output_path=output_path,
                        settings=settings,
                        progress_callback=lambda msg: progress_callback(f"[{audio_name}] {msg}") if progress_callback else None
                    )

                    individual_results.append({
                        'success': success,
                        'audio_file': audio_name,
                        'output_file': output_name,
                        'output_path': output_path
                    })

                    if success:
                        if progress_callback:
                            progress_callback(f"✅ 成功创建: {output_name}")
                    else:
                        if progress_callback:
                            progress_callback(f"❌ 创建失败: {output_name}")

                except Exception as e:
                    individual_results.append({
                        'success': False,
                        'audio_file': audio_name,
                        'output_file': output_name,
                        'error': str(e)
                    })

                    if progress_callback:
                        progress_callback(f"❌ 处理失败 {audio_name}: {str(e)}")

            # 统计结果
            successful_count = sum(1 for result in individual_results if result['success'])
            total_count = len(individual_results)

            if progress_callback:
                progress_callback(f"独立视频创建完成: {successful_count}/{total_count} 个文件成功")

            return {
                'success': successful_count > 0,
                'novel_name': novel_name,
                'mode': 'individual_videos',
                'total_files': total_count,
                'successful_files': successful_count,
                'failed_files': total_count - successful_count,
                'output_dir': output_dir,
                'individual_results': individual_results
            }

        except Exception as e:
            if progress_callback:
                progress_callback(f"创建独立视频失败: {str(e)}")

            return {
                'success': False,
                'novel_name': novel_name,
                'mode': 'individual_videos',
                'error': str(e)
            }

    def find_matching_subtitle(self, audio_file, subtitle_files):
        """
        查找与音频文件匹配的字幕文件

        参数:
            audio_file (str): 音频文件路径
            subtitle_files (list): 字幕文件列表

        返回:
            str: 匹配的字幕文件路径，如果没有找到则返回None
        """
        if not subtitle_files:
            return None

        # 提取音频文件的数字
        audio_name = os.path.basename(audio_file)
        audio_number = self.extract_number_from_filename(audio_name)

        # 查找具有相同数字的字幕文件
        for subtitle_file in subtitle_files:
            subtitle_name = os.path.basename(subtitle_file)
            subtitle_number = self.extract_number_from_filename(subtitle_name)

            if audio_number == subtitle_number:
                return subtitle_file

        return None
